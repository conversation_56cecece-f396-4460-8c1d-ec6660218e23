<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\DocumentType;
use App\Enums\Gender;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Date;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PersonalInformation>
 */
final class PersonalInformationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'full_name' => fake()->name(),
            'date_of_birth' => fake()->date(),
            'gender' => fake()->randomElement(Gender::cases()),
            'address' => fake()->address(),
            'document_type' => DocumentType::IdentityCard,
            'document_number' => fake()->unique()->numerify('############'),
            'document_issuing_place' => fake()->city(),
            'document_issue_date' => $issueDate = Date::now()->subYears(fake()->randomNumber(1)),
            'document_expiry_date' => $issueDate->addYears(20),
            'document_front_image' => mb_ltrim(fake()->filePath(), '/'),
            'document_back_image' => mb_ltrim(fake()->filePath(), '/'),
            'nationality' => 'vietnam',
        ];
    }
}
