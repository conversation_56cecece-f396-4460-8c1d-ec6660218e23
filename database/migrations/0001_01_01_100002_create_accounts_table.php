<?php

declare(strict_types=1);

use App\Support\Migrations\HasPersonalInformationRelationColumn;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration {
    use HasPersonalInformationRelationColumn;

    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('email')->nullable()->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone_number', 15)->unique();
            $table->timestamp('phone_number_verified_at')->nullable();
            $table->string('password');
            $table->boolean('is_super_administrator')->default(false);
            $table->rememberToken();
            $table->timestamps();

            $this->createPersonalInformationRelationColumn($table);
        });

        $this->createFirstAccount();
    }

    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }

    private function createFirstAccount(): void
    {
        DB::table('accounts')->insert([
            'id' => Str::uuid7(),
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'email_verified_at' => Date::now(),
            'phone_number' => '**********',
            'phone_number_verified_at' => Date::now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'is_super_administrator' => true,
            'created_at' => Date::now(),
            'updated_at' => Date::now(),
        ]);
    }
};
