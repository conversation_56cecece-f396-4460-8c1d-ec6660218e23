<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('personal_information', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('full_name');
            $table->date('date_of_birth');
            $table->string('gender', 20);
            $table->text('address');

            $table->string('document_type', 50);
            $table->string('document_number', 50);
            $table->string('document_issuing_place', 100);
            $table->date('document_issue_date');
            $table->date('document_expiry_date');
            $table->string('document_front_image', 2048);
            $table->string('document_back_image', 2048);

            $table->string('nationality', 50);

            $table->timestamps();
        });
    }
};
