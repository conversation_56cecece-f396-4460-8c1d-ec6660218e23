<?php

declare(strict_types=1);

use App\Enums\MerchantStatus;
use App\Support\Migrations\HasAccountRelationColumn;
use App\Support\Migrations\HasAddressComponentRelationColumn;
use App\Support\Migrations\HasMarketRelationColumn;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\ForeignKeyDefinition;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    use HasAccountRelationColumn;
    use HasAddressComponentRelationColumn;
    use HasMarketRelationColumn;

    public function up(): void
    {
        Schema::create('merchants', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createAccountRelationColumn($table);
            $this->createAddressComponentRelationColumn($table);
            $table->string('name');
            $table->float('rating', 1)->default(5);
            $table->string('contact_phone_number')->index();
            $table->string('contact_email')->nullable();
            $table->text('description')->nullable();
            $table->json('business_hours')->nullable();
            $table->string('status', 20)->default(MerchantStatus::Pending->value)->index();
            $table->timestamps();
        });

        Schema::create('merchant_images', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createMerchantRelationColumn($table);
            $table->string('path');
            $table->unsignedInteger('priority')->default(0);
            $table->timestamps();
        });

        Schema::create('merchant_categories', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('image_path', 2048)->nullable();
            $table->unsignedInteger('priority')->default(0);
            $table->timestamps();
        });

        Schema::create('merchant_category', function (Blueprint $table): void {
            $this->createMerchantRelationColumn($table)->cascadeOnDelete();
            $table->foreignUuid('merchant_category_id')->constrained()->cascadeOnDelete();
            $table->primary(['merchant_id', 'merchant_category_id']);
        });

        Schema::create('merchant_menu_categories', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createMerchantRelationColumn($table);
            $table->string('name');
            $table->unsignedInteger('priority')->default(0);
            $table->string('status')->index();
            $table->timestamps();
        });

        Schema::create('merchant_menu_items', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createMerchantRelationColumn($table);
            $table->foreignUuid('merchant_menu_category_id')->constrained();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedInteger('price');
            $table->string('image_path', 2048)->nullable();
            $table->unsignedInteger('priority')->default(0);
            $table->string('status')->index();
            $table->timestamps();
        });

        Schema::create('merchant_menu_modifier_groups', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createMerchantRelationColumn($table);
            $table->string('name');
            $table->unsignedInteger('selection_min')->default(0);
            $table->unsignedInteger('selection_max')->default(0);
            $table->unsignedInteger('priority')->default(0);
            $table->string('status')->index();
            $table->timestamps();
        });

        Schema::create('merchant_menu_modifiers', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createMarketRelationColumn($table);
            $this->createMerchantRelationColumn($table);
            $table->foreignUuid('merchant_menu_modifier_group_id')->constrained();
            $table->string('name');
            $table->unsignedInteger('price');
            $table->unsignedInteger('priority')->default(0);
            $table->string('status')->index();
            $table->timestamps();
        });

        Schema::create('merchant_menu_item_menu_modifier_group', function (Blueprint $table): void {
            $table->foreignUuid('merchant_menu_item_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('merchant_menu_modifier_group_id')->constrained()->cascadeOnDelete();
            $table->unsignedInteger('priority')->default(0);

            $table->primary(['merchant_menu_item_id', 'merchant_menu_modifier_group_id']);
        });
    }

    private function createMerchantRelationColumn(Blueprint $table): ForeignKeyDefinition
    {
        return $table->foreignUuid('merchant_id')->constrained();
    }
};
