<?php

declare(strict_types=1);

use App\Enums\AddressComponentType;
use App\Support\Migrations\HasAddressComponentRelationColumn;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    use HasAddressComponentRelationColumn;

    public function up(): void
    {
        Schema::create('address_components', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('name')->index();
            $table->string('place_id')->nullable()->index();
            $table->string('formatted_address')->index();
            $table->string('address');
            $table->string('administrative_area_level_3', 100)->nullable();
            $table->string('administrative_area_level_2', 100)->nullable();
            $table->string('administrative_area_level_1', 100)->nullable();
            $table->string('country', 100)->nullable();
            $table->string('postal_code', 20)->nullable();
            $table->decimal('latitude', 10, 8)->nullable()->index();
            $table->decimal('longitude', 11, 8)->nullable()->index();
            $table->string('source', 10)->default(AddressComponentType::Manual->value);
        });

        Schema::create('countries', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createAddressComponentRelationColumn($table);
            $table->string('name')->index();
            $table->string('code', 2)->index();
        });

        Schema::create('administrative_area_level_1', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createAddressComponentRelationColumn($table);
            $table->string('name')->index();
            $table->foreignUuid('country_id')->index();
        });

        Schema::create('administrative_area_level_2', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $this->createAddressComponentRelationColumn($table);
            $table->string('name')->index();
            $table->foreignUuid('country_id')->index();
            $table->foreignUuid('administrative_area_level_1')->index();
        });
    }
};
