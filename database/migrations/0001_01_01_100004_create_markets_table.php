<?php

declare(strict_types=1);

use App\Enums\MarketStatus;
use App\Support\Migrations\HasAccountRelationColumn;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    use HasAccountRelationColumn;

    public function up(): void
    {
        Schema::create('markets', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $this->createAccountRelationColumn($table);
            $table->date('opening_date')->nullable();
            $table->string('status', 20)->default(MarketStatus::Pending->value)->index();
            $table->timestamps();
        });

        Schema::create('market_administrative_areas', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->foreignUuid('market_id')->constrained();
            $table
                ->foreignUuid('administrative_area_level_1')
                ->constrained('administrative_area_level_1', indexName: 'market_administrative_area_level_1_fk');
            $table
                ->foreignUuid('administrative_area_level_2')
                ->constrained('administrative_area_level_2', indexName: 'market_administrative_area_level_2_fk');

            $table->unique(['market_id', 'administrative_area_level_1', 'administrative_area_level_2']);
        });
    }
};
