<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Account;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        Account::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }
}
