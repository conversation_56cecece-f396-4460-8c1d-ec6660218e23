# Resource Routes

Example:

- Get the list of resources
  - GET `/users`
  - Action: `User<PERSON>ontroller@index`
  - Name: `users.index`
- Create a new resource (view)
  - GET `/users/create`
  - Action: `UserController@create`
  - Name: `users.create`
- Store a new resource
  - POST `/users/create`
  - Action: `UserController@store`
  - Name: `users.store`
- Edit a resource (view)
  - GET `/users/{user}/edit`
  - Action: `UserController@edit`
  - Name: `users.edit`
- Update a resource
  - POST/PUT/PATCH `/users/{user}/edit`
  - Action: `UserController@update`
  - Name: `users.update`
- Delete a resource
  - DELETE `/users/{user}`
  - Action: `UserController@destroy`
  - Name: `users.destroy`
