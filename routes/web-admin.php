<?php

declare(strict_types=1);

use App\Http\Controllers\Admin\AccountController;
use App\Http\Controllers\Admin\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Admin\CustomerController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\GettingStartedController;
use App\Http\Controllers\Admin\Helpers\SelectionAccountController;
use App\Http\Controllers\Admin\Helpers\SelectionAdministrativeAreaLevel2Controller;
use App\Http\Controllers\Admin\Helpers\SelectionGoogleMapsPlaceController;
use App\Http\Controllers\Admin\Helpers\SelectionMerchantMenuCategoryController;
use App\Http\Controllers\Admin\IndexController;
use App\Http\Controllers\Admin\MarketController;
use App\Http\Controllers\Admin\Merchants\MerchantCategoryController;
use App\Http\Controllers\Admin\Merchants\MerchantController;
use App\Http\Controllers\Admin\Merchants\MerchantMenuCategoryController;
use App\Http\Controllers\Admin\Merchants\MerchantMenuItemController;
use App\Http\Controllers\Admin\Merchants\MerchantMenuModifierGroupController;
use App\Http\Controllers\Admin\Merchants\UpdateSellingTimeController;
use App\Http\Controllers\Admin\Settings\AppearanceController;
use App\Http\Controllers\Admin\Settings\PasswordController;
use App\Http\Controllers\Admin\Settings\ProfileController;
use App\Http\Middleware\HasMarket;
use App\Http\Middleware\HasMarkets;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', [IndexController::class, '__invoke'])
    ->name('index');

Route::middleware(['auth:web_admin', 'verified', HasMarkets::class, HasMarket::class])->group(function () {
    Route::get('getting-started', [GettingStartedController::class, '__invoke'])
        ->name('getting-started');

    // Profile and Settings Routes.

    Route::get('settings/profile', [ProfileController::class, 'edit'])
        ->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])
        ->name('profile.update');

    Route::get('settings/password', [PasswordController::class, 'edit'])
        ->name('password.edit');
    Route::put('settings/password', [PasswordController::class, 'update'])
        ->name('password.update');

    Route::get('settings/appearance', [AppearanceController::class, 'edit'])
        ->name('appearance');

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
        ->name('logout');

    // System Routes.

    // Customer Routes.

    Route::get('customers', [CustomerController::class, 'index'])
        ->name('customers.index');

    Route::get('customers/create', [CustomerController::class, 'create'])
        ->name('customers.create');

    Route::post('customers/create', [CustomerController::class, 'store'])
        ->name('customers.store');

    Route::get('customers/{customer}/edit', [CustomerController::class, 'edit'])
        ->name('customers.edit');

    Route::put('customers/{customer}/edit', [CustomerController::class, 'update'])
        ->name('customers.update');

    // Account Routes.

    Route::get('accounts', [AccountController::class, 'index'])
        ->name('accounts.index');

    Route::get('accounts/create', [AccountController::class, 'create'])
        ->name('accounts.create');

    Route::post('accounts/create', [AccountController::class, 'store'])
        ->name('accounts.store');

    Route::get('accounts/{account}/edit', [AccountController::class, 'edit'])
        ->name('accounts.edit');

    Route::put('accounts/{account}/edit', [AccountController::class, 'update'])
        ->name('accounts.update');

    // Market Routes.

    Route::get('markets', [MarketController::class, 'index'])
        ->name('markets.index');

    Route::get('markets/create', [MarketController::class, 'create'])
        ->name('markets.create');

    Route::post('markets/create', [MarketController::class, 'store'])
        ->name('markets.store');

    Route::get('markets/{market}/edit', [MarketController::class, 'edit'])
        ->name('markets.edit');

    Route::put('markets/{market}/edit', [MarketController::class, 'update'])
        ->name('markets.update');

    // Merchant Category Routes.

    Route::get('merchant-categories', [MerchantCategoryController::class, 'index'])
        ->name('merchant-categories.index');

    Route::get('merchant-categories/create', [MerchantCategoryController::class, 'create'])
        ->name('merchant-categories.create');

    Route::post('merchant-categories/create', [MerchantCategoryController::class, 'store'])
        ->name('merchant-categories.store');

    Route::get('merchant-categories/{merchant_category}/edit', [MerchantCategoryController::class, 'edit'])
        ->name('merchant-categories.edit');

    Route::post('merchant-categories/{merchant_category}/edit', [MerchantCategoryController::class, 'update'])
        ->name('merchant-categories.update');

    // Grouped Market Routes.

    Route::prefix('markets/{currentMarket}')->group(callback: function () {
        Route::get('dashboard', [DashboardController::class, '__invoke'])
            ->name('dashboard');

        Route::get('merchants', [MerchantController::class, 'index'])
            ->name('merchants.index');
        Route::get('merchants/create', [MerchantController::class, 'create'])
            ->name('merchants.create');
        Route::post('merchants/create', [MerchantController::class, 'store'])
            ->name('merchants.store');
        Route::get('merchants/{merchant}', [MerchantController::class, 'show'])
            ->name('merchants.show');
        Route::get('merchants/{merchant}/edit', [MerchantController::class, 'edit'])
            ->name('merchants.edit');
        Route::post('merchants/{merchant}/edit', [MerchantController::class, 'update'])
            ->name('merchants.update');

        Route::put('{merchant}/selling-times', [UpdateSellingTimeController::class, '__invoke'])
            ->name('merchants.selling-times.update');

        Route::group(['prefix' => 'merchants/{merchant}', 'as' => 'merchants.'], function () {
            // Merchant Menu Items Routes.

            Route::get('menus', [MerchantMenuItemController::class, 'index'])
                ->name('menus.index');
            Route::get('menus/create', [MerchantMenuItemController::class, 'create'])
                ->name('menus.create');
            Route::post('menus/create', [MerchantMenuItemController::class, 'store'])
                ->name('menus.store');
            Route::get('menus/{menu}/edit', [MerchantMenuItemController::class, 'edit'])
                ->name('menus.edit');
            Route::post('menus/{menu}/edit', [MerchantMenuItemController::class, 'update'])
                ->name('menus.update');

            // Merchant Menu Categories Routes.

            Route::get('menu-categories', [MerchantMenuCategoryController::class, 'index'])
                ->name('menu-categories.index');
            Route::get('menu-categories/create', [MerchantMenuCategoryController::class, 'create'])
                ->name('menu-categories.create');
            Route::post('menu-categories/create', [MerchantMenuCategoryController::class, 'store'])
                ->name('menu-categories.store');
            Route::get('menu-categories/{menu_category}/edit', [MerchantMenuCategoryController::class, 'edit'])
                ->name('menu-categories.edit');
            Route::put('menu-categories/{menu_category}/edit', [MerchantMenuCategoryController::class, 'update'])
                ->name('menu-categories.update');

            // Merchant Menu Modifier Groups Routes.

            Route::get('menu-modifier-groups', [MerchantMenuModifierGroupController::class, 'index'])
                ->name('menu-modifier-groups.index');
            Route::get('menu-modifier-groups/create', [MerchantMenuModifierGroupController::class, 'create'])
                ->name('menu-modifier-groups.create');
            Route::post('menu-modifier-groups/create', [MerchantMenuModifierGroupController::class, 'store'])
                ->name('menu-modifier-groups.store');
            Route::get('menu-modifier-groups/{menu_modifier_group}/edit', [MerchantMenuModifierGroupController::class, 'edit'])
                ->name('menu-modifier-groups.edit');
            Route::put('menu-modifier-groups/{menu_modifier_group}/edit', [MerchantMenuModifierGroupController::class, 'update'])
                ->name('menu-modifier-groups.update');
        });
    });

    // Helpers Routes.

    Route::prefix('_helpers')->as('helpers.')->group(function () {
        Route::prefix('selections')->as('selections.')->group(function () {
            Route::get('accounts', [SelectionAccountController::class, 'index'])
                ->name('accounts.index');

            Route::get('google-maps/places', [SelectionGoogleMapsPlaceController::class, 'index'])
                ->name('google-maps.index');

            Route::get('google-maps/places/{placeId}', [SelectionGoogleMapsPlaceController::class, 'show'])
                ->name('google-maps.show');

            Route::get(
                'administrative-areas-level-2',
                [SelectionAdministrativeAreaLevel2Controller::class, 'index'],
            )
                ->name('administrative-areas-level-2.index');

            Route::get('merchant-menu-categories', [SelectionMerchantMenuCategoryController::class, 'index'])
                ->name('merchant-menu-categories.index');
        });
    });

    // Example Routes.

    Route::get('example', function () {
        return Inertia::render('admin/Example');
    });

    Route::post('example/store', function () {
        dd(request()->all());
    })->name('example.store');
});

Route::middleware('guest:web_admin')->group(function () {
    Route::get('login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store'])
        ->name('login.store');
});
