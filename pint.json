{"preset": "per", "rules": {"array_push": true, "backtick_to_shell_exec": true, "date_time_immutable": true, "declare_strict_types": true, "ordered_imports": {"sort_algorithm": "alpha", "imports_order": ["class", "const", "function"]}, "final_class": true, "final_internal_class": true, "mb_str_functions": true, "modernize_types_casting": true, "no_superfluous_elseif": true, "no_useless_else": true, "protected_to_private": true, "self_accessor": true, "self_static_accessor": true, "strict_comparison": true}}