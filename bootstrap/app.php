<?php

declare(strict_types=1);

use App\Http\Middleware\HandleAdminInertiaRequests;
use App\Http\Middleware\HandleAdminRequests;
use App\Http\Middleware\HandleApiRequests;
use App\Http\Middleware\HandleAppearance;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        using: function (ConfigRepository $config): void {
            Route::middleware([
                HandleApiRequests::class,
                'api',
            ])
                ->domain($config->get('platform.api.domain', ''))
                ->prefix($config->get('platform.api.prefix', ''))
                ->group(base_path('routes/api.php'));

            Route::middleware([
                HandleAdminRequests::class,
                'web',
                HandleAppearance::class,
                HandleAdminInertiaRequests::class,
            ])
                ->domain($config->get('platform.admin.domain', ''))
                ->prefix($config->get('platform.admin.prefix', ''))
                ->as('admin.')
                ->group(base_path('routes/web-admin.php'));

            Route::middleware(['web'])
                ->group(base_path('routes/web.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->web(append: [
            AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->redirectTo(
            guests: function (): string {
                if (Context::get('admin')) {
                    return route('admin.login');
                }

                throw new LogicException('No route for guests.');
            },
        );
    })
    ->withCommands()
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
