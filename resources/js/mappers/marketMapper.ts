import { Market, MarketFormData } from '@/types';

export const MarketMapper = {
    fromModelToFormData: (model: Market): MarketFormData => {
        return {
            name: model.name,
            description: model.description || '',
            opening_date: model.opening_date ? model.opening_date.slice(0, 10) : '',
            account_id: model.account_id,
            administrative_area: {
                administrative_area_level_1: {
                    id: model.administrative_areas?.[0]?.administrative_area_level_1 || '',
                    name: model.administrative_areas?.[0]?.administrative_area_level1?.name || '',
                },
                administrative_areas_level_2:
                    model.administrative_areas?.map((area) => ({
                        id: area.administrative_area_level_2,
                        name: area.administrative_area_level2?.name || '',
                    })) || [],
            },
            status: model.status,
        };
    },
};
