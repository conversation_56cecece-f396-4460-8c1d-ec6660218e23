<script setup lang="ts">
import DataTable from '@/components/admin/markets/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import { type BreadcrumbItem, type Market } from '@/types';
import { Head } from '@inertiajs/vue3';

interface Props {
    markets: Market[];
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Markets'),
        href: route('admin.markets.index'),
    },
];
</script>

<template>
    <Head :title="t('Markets')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6" v-if="markets.length > 0">
            <DataTable :markets="markets" />
        </div>
        <div class="px-4 py-6" v-else>
            <GettingStartedResourceCard :title="t('No markets yet')" :description="t('Get started and create your first market.')">
                <GettingStartedResourceAction :href="route('admin.markets.create')">
                    {{ t('Create new market') }}
                </GettingStartedResourceAction>
            </GettingStartedResourceCard>
        </div>
    </AdminLayout>
</template>
