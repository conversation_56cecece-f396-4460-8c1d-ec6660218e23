<script setup lang="ts">
import MarketForm from '@/components/admin/markets/MarketForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import { MarketMapper } from '@/mappers/marketMapper';
import type { AdministrativeAreaLevel1, BreadcrumbItem, Market, MarketFormData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    market: Market;
}

const { market } = defineProps<Props>();

const page = usePage<{
    administrativeAreasLevel1: AdministrativeAreaLevel1[];
}>();

const formData: MarketFormData = MarketMapper.fromModelToFormData(market);

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Markets'),
        href: route('admin.markets.index'),
    },
    {
        title: t('Edit market'),
        href: route('admin.markets.edit', market.id),
    },
];

const submit = (form: InertiaForm<MarketFormData>) => {
    form.put(route('admin.markets.update', market.id), {
        preserveScroll: true,
    });
};
</script>

<template>
    <Head :title="t('Edit market')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Edit market')" />

            <div class="flex-1 md:max-w-2xl">
                <MarketForm
                    :model="market"
                    :form-data="formData"
                    :administrative-areas-level1="page.props.administrativeAreasLevel1"
                    @submit="submit"
                />
            </div>
        </div>
    </AdminLayout>
</template>
