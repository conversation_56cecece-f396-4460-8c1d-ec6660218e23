<script setup lang="ts">
import MarketForm from '@/components/admin/markets/MarketForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { AdministrativeAreaLevel1, BreadcrumbItem, MarketFormData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Markets'),
        href: route('admin.markets.index'),
    },
    {
        title: t('Create new market'),
        href: route('admin.markets.create'),
    },
];

const page = usePage<{ administrativeAreasLevel1: AdministrativeAreaLevel1[] }>();

const formData: MarketFormData = {
    name: '',
    description: '',
    opening_date: '',
    administrative_area: {
        administrative_area_level_1: {
            id: '',
            name: '',
        },
        administrative_areas_level_2: [],
    },
    account_id: '',
    status: 'pending',
};

const submit = (form: InertiaForm<MarketFormData>) => {
    form.post(route('admin.markets.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head :title="t('Create new market')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Create new market')" />

            <div class="flex-1 md:max-w-2xl">
                <MarketForm :form-data="formData" :administrative-areas-level1="page.props.administrativeAreasLevel1" @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
