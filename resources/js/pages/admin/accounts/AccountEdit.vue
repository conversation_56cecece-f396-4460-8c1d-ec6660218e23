<script setup lang="ts">
import AccountForm from '@/components/admin/accounts/AccountForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { Account, AccountFormData, BreadcrumbItem } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

interface Props {
    account: Account;
}

const { account } = defineProps<Props>();

const formData: AccountFormData = {
    name: account.name,
    phone_number: account.phone_number,
    email: account.email || '',
    password: '',
    password_confirmation: '',
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Accounts'),
        href: route('admin.accounts.index'),
    },
    {
        title: t('Edit account'),
        href: route('admin.accounts.edit', account.id),
    },
];

const submit = (form: InertiaForm<AccountFormData>) => {
    form.put(route('admin.accounts.update', account.id), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset('password', 'password_confirmation');
        },
    });
};
</script>

<template>
    <Head :title="t('Edit account')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Edit account')" />

            <div class="flex-1 md:max-w-2xl">
                <AccountForm :form-data="formData" @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
