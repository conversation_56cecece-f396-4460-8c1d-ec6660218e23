<script setup lang="ts">
import AccountForm from '@/components/admin/accounts/AccountForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { AccountFormData, BreadcrumbItem } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Accounts'),
        href: route('admin.accounts.index'),
    },
    {
        title: t('Create new account'),
        href: route('admin.accounts.create'),
    },
];

const formData: AccountFormData = {
    name: '',
    phone_number: '',
    email: '',
    password: '',
    password_confirmation: '',
};

const submit = (form: InertiaForm<AccountFormData>) => {
    form.post(route('admin.accounts.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head :title="t('Create new account')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Create new account')" />

            <div class="flex-1 md:max-w-2xl">
                <AccountForm :form-data="formData" @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
