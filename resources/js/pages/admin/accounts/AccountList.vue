<script setup lang="ts">
import DataTable from '@/components/admin/accounts/DataTable.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import { type Account, type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';

interface Props {
    accounts: Account[];
}

const { accounts } = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Accounts'),
        href: route('admin.accounts.index'),
    },
];
</script>

<template>
    <Head :title="t('Accounts')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <DataTable :accounts="accounts" />
        </div>
    </AdminLayout>
</template>
