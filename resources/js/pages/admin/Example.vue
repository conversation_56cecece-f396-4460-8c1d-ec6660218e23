<script setup lang="ts">
import InputAddressComponent from '@/components/admin/InputAddressComponent.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { InputImage } from '@/components/ui/input-image';
import { Label } from '@/components/ui/label';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { AddressComponentFormData } from '@/types';
import { Head, InertiaForm, useForm } from '@inertiajs/vue3';

type FormData = {
    image: File | null;
    address_component: AddressComponentFormData;
};

const formData: FormData = {
    image: null,
    address_component: {
        name: '',
        place_id: '',
        formatted_address: '',
        address: '',
        administrative_area_level_3: '',
        administrative_area_level_2: '',
        administrative_area_level_1: '',
        country: '',
        postal_code: '',
        latitude: null,
        longitude: null,
        source: 'google',
    },
};

const form = useForm<FormData>(formData);

const submit = (form: InertiaForm<FormData>) => {
    form.post(route('admin.example.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head :title="t('Example Form')" />

    <AdminLayout>
        <div class="px-4 py-6">
            <pre>{{ form }}</pre>
        </div>

        <div class="px-4 py-6">
            <div class="flex-1 md:max-w-2xl">
                <form @submit.prevent="submit(form)" class="space-y-6" enctype="multipart/form-data">
                    <div class="grid gap-2">
                        <Label for="image">{{ t('Image') }}</Label>

                        <InputImage id="image" v-model="form.image" :disabled="form.processing" />

                        <InputError :message="form.errors.image" />
                    </div>

                    <div class="grid gap-2">
                        <Label for="address">{{ t('Address') }}</Label>

                        <InputAddressComponent id="address" v-model="form.address_component" :disabled="form.processing" />

                        <InputError :message="form.errors.image" />
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
                        </Transition>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
