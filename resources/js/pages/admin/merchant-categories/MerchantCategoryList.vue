<script setup lang="ts">
import DataTable from '@/components/admin/merchant-categories/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, MerchantCategory } from '@/types';
import { Head } from '@inertiajs/vue3';

interface Props {
    merchantCategories: MerchantCategory[];
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchant categories'),
        href: route('admin.merchant-categories.index'),
    },
];
</script>

<template>
    <Head :title="t('Merchant categories')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6" v-if="merchantCategories.length > 0">
            <DataTable :merchant-categories="merchantCategories" />
        </div>
        <div class="px-4 py-6" v-else>
            <GettingStartedResourceCard
                :title="t('No merchant categories yet')"
                :description="t('Get started and create your first merchant category.')"
            >
                <GettingStartedResourceAction :href="route('admin.merchant-categories.create')">
                    {{ t('Create new merchant category') }}
                </GettingStartedResourceAction>
            </GettingStartedResourceCard>
        </div>
    </AdminLayout>
</template>
