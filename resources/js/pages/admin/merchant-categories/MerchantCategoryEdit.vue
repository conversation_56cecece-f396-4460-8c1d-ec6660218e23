<script setup lang="ts">
import MerchantCategoryForm from '@/components/admin/merchant-categories/MerchantCategoryForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, MerchantCategory, MerchantCategoryFormData } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

interface Props {
    merchantCategory: MerchantCategory;
}

const { merchantCategory } = defineProps<Props>();

const formData: MerchantCategoryFormData = {
    name: merchantCategory.name,
    image: null,
    image_path: merchantCategory.image_path,
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchant categories'),
        href: route('admin.merchant-categories.index'),
    },
    {
        title: t('Edit merchant category'),
        href: route('admin.merchant-categories.edit', merchantCategory.id),
    },
];

const submit = (form: InertiaForm<MerchantCategoryFormData>) => {
    form.post(route('admin.merchant-categories.update', merchantCategory.id), {
        preserveScroll: true,
    });
};
</script>

<template>
    <Head :title="t('Edit merchant category')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Edit merchant category')" />

            <div class="flex-1 md:max-w-2xl">
                <MerchantCategoryForm :model="merchantCategory" :form-data="formData" @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
