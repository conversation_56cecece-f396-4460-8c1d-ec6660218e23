<script setup lang="ts">
import MerchantCategoryForm from '@/components/admin/merchant-categories/MerchantCategoryForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, MerchantCategoryFormData } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchant categories'),
        href: route('admin.merchant-categories.index'),
    },
    {
        title: t('Create new merchant category'),
        href: route('admin.merchant-categories.create'),
    },
];

const submit = (form: InertiaForm<MerchantCategoryFormData>) => {
    form.post(route('admin.merchant-categories.store'), {
        preserveScroll: true,
    });
};
</script>

<template>
    <Head :title="t('Create new merchant category')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Create new merchant category')" />

            <div class="flex-1 md:max-w-2xl">
                <MerchantCategoryForm @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
