<script setup lang="ts">
import { Head } from '@inertiajs/vue3';

import AppearanceTabs from '@/components/AppearanceTabs.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';

import AdminLayout from '@/layouts/AdminLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { t } from '@/lib/utils';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: t('Appearance settings'),
        href: route('admin.appearance'),
    },
];
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbItems">
        <Head :title="t('Appearance settings')" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall :title="t('Appearance settings')" :description="t('Update your account\'s appearance settings')" />
                <AppearanceTabs />
            </div>
        </SettingsLayout>
    </AdminLayout>
</template>
