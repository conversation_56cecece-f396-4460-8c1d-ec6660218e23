<script setup lang="ts">
import { Head, useForm, usePage } from '@inertiajs/vue3';

import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AdminLayout from '@/layouts/AdminLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, SharedData, User } from '@/types';

type UserFormData = Pick<User, 'name' | 'email'> & { email: string };

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Profile settings'),
        href: route('admin.profile.edit'),
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

const form = useForm<UserFormData>({
    name: user.name,
    email: user.email || '',
});

const submit = () => {
    form.patch(route('admin.profile.update'), {
        preserveScroll: true,
    });
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Profile settings')" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall :title="t('Profile information')" :description="t('Update your name and email address')" />

                <form @submit.prevent="submit" class="space-y-6">
                    <div class="grid gap-2">
                        <Label for="name">{{ t('Name') }}</Label>
                        <Input id="name" class="mt-1 block w-full" v-model="form.name" required autocomplete="name" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="grid gap-2">
                        <Label for="email">{{ t('Email address') }}</Label>
                        <Input id="email" type="email" class="mt-1 block w-full" v-model="form.email" required autocomplete="username" />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing">{{ t('Save') }}</Button>

                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
                        </Transition>
                    </div>
                </form>
            </div>
        </SettingsLayout>
    </AdminLayout>
</template>
