<script setup lang="ts">
import { Button } from '@/components/ui/button';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import { Head, Link } from '@inertiajs/vue3';
import { Box, CirclePlus } from 'lucide-vue-next';
</script>

<template>
    <Head :title="t('Dashboard')" />

    <AdminLayout>
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <div class="flex h-full w-full flex-1 flex-col items-center justify-center gap-2">
                <Box :size="64" class="text-muted-foreground" />

                <h3 class="text-lg font-medium">{{ t('No markets yet') }}</h3>

                <p class="text-muted-foreground mb-2 text-sm">{{ t('Get started and create your first market.') }}</p>

                <Link :href="route('admin.markets.create')" class="text-center">
                    <Button>
                        <CirclePlus :size="16" />

                        {{ t('Create new market') }}
                    </Button>
                </Link>
            </div>
        </div>
    </AdminLayout>
</template>
