<script setup lang="ts">
import DataTable from '@/components/admin/customers/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Customer } from '@/types';
import { Head } from '@inertiajs/vue3';

interface Props {
    customers: Customer[];
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Customers'),
        href: route('admin.customers.index'),
    },
];
</script>

<template>
    <Head :title="t('Customers')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6" v-if="customers.length > 0">
            <DataTable :customers="customers" />
        </div>
        <div class="px-4 py-6" v-else>
            <GettingStartedResourceCard :title="t('No customers yet')" :description="t('Get started and create your first customer.')">
                <GettingStartedResourceAction :href="route('admin.customers.create')">
                    {{ t('Create new customer') }}
                </GettingStartedResourceAction>
            </GettingStartedResourceCard>
        </div>
    </AdminLayout>
</template>
