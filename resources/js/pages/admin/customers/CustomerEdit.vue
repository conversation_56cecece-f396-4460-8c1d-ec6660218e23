<script setup lang="ts">
import CustomerForm from '@/components/admin/customers/CustomerForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Customer, CustomerFormData } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

interface Props {
    customer: Customer;
}

const { customer } = defineProps<Props>();

const formData: CustomerFormData = {
    name: customer.name,
    email: customer.email || '',
    phone_number: customer.phone_number,
    password: '',
    password_confirmation: '',
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Customers'),
        href: route('admin.customers.index'),
    },
    {
        title: t('Edit Customer'),
        href: route('admin.customers.edit', customer.id),
    },
];

const submit = (form: InertiaForm<CustomerFormData>) => {
    form.put(route('admin.customers.update', customer.id), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset('password', 'password_confirmation');
        },
    });
};
</script>

<template>
    <Head :title="t('Edit Customer')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Edit Customer')" />

            <div class="flex-1 md:max-w-2xl">
                <CustomerForm :form-data="formData" @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
