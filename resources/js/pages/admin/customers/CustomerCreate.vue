<script setup lang="ts">
import CustomerForm from '@/components/admin/customers/CustomerForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, CustomerFormData } from '@/types';
import { Head, InertiaForm } from '@inertiajs/vue3';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Customers'),
        href: route('admin.customers.index'),
    },
    {
        title: t('Create new customer'),
        href: route('admin.customers.create'),
    },
];

const submit = (form: InertiaForm<CustomerFormData>) => {
    form.post(route('admin.customers.store'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head :title="t('Create new customer')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Create new customer')" />

            <div class="flex-1 md:max-w-2xl">
                <CustomerForm @submit="submit" />
            </div>
        </div>
    </AdminLayout>
</template>
