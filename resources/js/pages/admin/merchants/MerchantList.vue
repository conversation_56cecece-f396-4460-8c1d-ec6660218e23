<script setup lang="ts">
import DataTable from '@/components/admin/merchants/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Market, Merchant, SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';

interface Props {
    merchants: Merchant[];
}

defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
];
</script>

<template>
    <Head :title="t('Merchants')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6" v-if="merchants.length > 0">
            <DataTable :merchants="merchants" :market="currentMarket as Market" />
        </div>
        <div class="px-4 py-6" v-else>
            <GettingStartedResourceCard :title="t('No merchants yet')" :description="t('Get started and create your first merchant.')">
                <GettingStartedResourceAction :href="route('admin.merchants.create', { currentMarket: currentMarket?.id })">
                    {{ t('Create new merchant') }}
                </GettingStartedResourceAction>
            </GettingStartedResourceCard>
        </div>
    </AdminLayout>
</template>
