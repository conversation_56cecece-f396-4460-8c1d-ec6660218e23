<script setup lang="ts">
import MerchantBusinessHoursForm from '@/components/admin/merchants/MerchantBusinessHoursForm.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogScrollContent, DialogTrigger } from '@/components/ui/dialog';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
}

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const { merchant } = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];
</script>

<template>
    <Head :title="t('Merchant')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <MerchantLayout :merchant="merchant">
            <div class="space-y-6">
                <div class="flex items-center justify-between gap-2">
                    <HeadingSmall :title="t('Merchant Details')" :description="t('View all basic merchant information')" />

                    <Link :href="route('admin.merchants.edit', { currentMarket: currentMarket?.id, merchant: merchant.id })">
                        <Button variant="outline">{{ t('Edit') }}</Button>
                    </Link>
                </div>

                <Card class="w-full shadow-none">
                    <CardHeader>
                        <CardTitle>{{ merchant.name }}</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                        <div v-if="merchant.categories.length" class="flex items-center gap-2">
                            <div class="mt-3 flex flex-row gap-2">
                                <Badge v-for="category in merchant.categories" :key="category.id" variant="outline">
                                    {{ category.name }}
                                </Badge>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="flex min-w-48 flex-col text-sm font-medium">{{ t('Address') }}</div>
                            <span v-if="merchant.address_component" class="text-sm">{{ merchant.address_component.formatted_address }}</span>
                            <span v-else class="text-muted-foreground text-sm">{{ t('N/A') }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="flex min-w-48 flex-col text-sm font-medium">{{ t('Contact phone number') }}</div>
                            <span v-if="merchant.contact_phone_number" class="text-sm">{{ merchant.contact_phone_number }}</span>
                            <span v-else class="text-muted-foreground text-sm">{{ t('N/A') }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="flex min-w-48 flex-col text-sm font-medium">{{ t('Contact email') }}</div>
                            <span v-if="merchant.contact_email" class="text-sm">{{ merchant.contact_email }}</span>
                            <span v-else class="text-muted-foreground text-sm">{{ t('N/A') }}</span>
                        </div>
                    </CardContent>
                </Card>

                <div class="flex items-center justify-between">
                    <HeadingSmall :title="t('Business hours')" :description="t('View and manage merchant business hours')" />

                    <Dialog>
                        <DialogTrigger asChild>
                            <Button variant="outline">{{ t('Edit') }}</Button>
                        </DialogTrigger>
                        <DialogScrollContent>
                            <MerchantBusinessHoursForm :merchant="merchant" />
                        </DialogScrollContent>
                    </Dialog>
                </div>
            </div>
        </MerchantLayout>
    </AdminLayout>
</template>
