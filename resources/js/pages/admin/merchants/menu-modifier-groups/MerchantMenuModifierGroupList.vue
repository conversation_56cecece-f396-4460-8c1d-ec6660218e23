<script setup lang="ts">
import DataTable from '@/components/admin/merchant-menu-modifier-groups/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, MerchantMenuModifierGroup, SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    menuModifierGroups: MerchantMenuModifierGroup[];
}

const { merchant, menuModifierGroups } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menu modifier groups'),
        href: route('admin.merchants.menu-modifier-groups.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Menu modifier groups')" />

        <MerchantLayout :merchant="merchant" layout="full">
            <div class="px-4 py-6" v-if="menuModifierGroups.length > 0">
                <DataTable :merchant-menu-modifier-groups="menuModifierGroups" :merchant="merchant" />
            </div>
            <div class="px-4 py-6" v-else>
                <GettingStartedResourceCard
                    :title="t('No merchant menu modifiers yet')"
                    :description="t('Get started and create your first merchant menu modifiers.')"
                >
                    <GettingStartedResourceAction
                        :href="route('admin.merchants.menu-modifier-groups.create', { currentMarket: currentMarket?.id, merchant: merchant.id })"
                    >
                        {{ t('Create new menu modifier') }}
                    </GettingStartedResourceAction>
                </GettingStartedResourceCard>
            </div>
        </MerchantLayout>
    </AdminLayout>
</template>

<style scoped></style>
