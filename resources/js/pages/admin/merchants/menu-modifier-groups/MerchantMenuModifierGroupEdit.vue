<script setup lang="ts">
import MerchantMenuModifierGroupForm from '@/components/admin/merchant-menu-modifier-groups/MerchantMenuModifierGroupForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, MerchantMenuModifierGroupFormData, MerchantMenuModifierItem, SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    menuModifierGroup: {
        id: number;
        name: string;
        selection_min: number;
        selection_max: number;
        priority: number;
        status: 'activated' | 'deactivated';
        items: MerchantMenuModifierItem[];
    };
}

const { merchant, menuModifierGroup } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const formData: MerchantMenuModifierGroupFormData = {
    name: menuModifierGroup.name,
    selection_min: menuModifierGroup.selection_min,
    selection_max: menuModifierGroup.selection_max,
    priority: menuModifierGroup.priority,
    status: menuModifierGroup.status,
    modifier_items: menuModifierGroup.items.map((item) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        status: item.status,
    })),
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menu modifier groups'),
        href: route('admin.merchants.menu-modifier-groups.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Edit menu modifier group'),
        href: route('admin.merchants.menu-modifier-groups.edit', {
            currentMarket: currentMarket?.id,
            merchant: merchant.id,
            menu_modifier_group: menuModifierGroup.id,
        }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuModifierGroupFormData>) => {
    form.put(
        route('admin.merchants.menu-modifier-groups.update', {
            currentMarket: currentMarket?.id,
            merchant: merchant.id,
            menu_modifier_group: menuModifierGroup.id,
        }),
        {
            preserveScroll: true,
        },
    );
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Edit menu modifier group')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuModifierGroupForm :form-data="formData" @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>

<style scoped></style>
