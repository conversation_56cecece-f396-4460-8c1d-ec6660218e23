<script setup lang="ts">
import MerchantMenuModifierGroupForm from '@/components/admin/merchant-menu-modifier-groups/MerchantMenuModifierGroupForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, MerchantMenuModifierGroupFormData, SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
}

const { merchant } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { merchant: merchant.id, currentMarket: currentMarket?.id }),
    },
    {
        title: t('Menu modifier groups'),
        href: route('admin.merchants.menu-modifier-groups.index', { merchant: merchant.id, currentMarket: currentMarket?.id }),
    },
    {
        title: t('Create menu modifier group'),
        href: route('admin.merchants.menu-modifier-groups.create', { merchant: merchant.id, currentMarket: currentMarket?.id }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuModifierGroupFormData>) => {
    form.post(route('admin.merchants.menu-modifier-groups.store', { merchant: merchant.id, currentMarket: currentMarket?.id }), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Create menu modifier group')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuModifierGroupForm @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>

<style scoped></style>
