<script setup lang="ts">
import MerchantForm from '@/components/admin/merchants/MerchantForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { AddressComponent, BreadcrumbItem, CheckboxItem, Merchant, MerchantFormData, SharedData } from '@/types';
import { Head, InertiaForm, useForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    categories: CheckboxItem[];
}

const { merchant, categories } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: t('Edit merchant'),
        href: route('admin.merchants.edit', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];

const form = useForm<MerchantFormData>({
    name: merchant.name,
    account_id: merchant.account_id,
    address_component: merchant.address_component as AddressComponent,
    category_ids: merchant.categories?.map((category) => category.id) || [],
    contact_email: merchant.contact_email || '',
    contact_phone_number: merchant.contact_phone_number || '',
    description: merchant.description || '',
    status: merchant.status,
    images: [],
});

const handleSubmit = (form: InertiaForm<MerchantFormData>) => {
    form.post(route('admin.merchants.update', { currentMarket: currentMarket?.id, merchant: merchant.id }), {
        preserveScroll: true,
    });
};
</script>

<template>
    <Head :title="t('Edit Merchant')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <MerchantLayout :merchant="merchant">
            <Heading :title="t('Edit Merchant')" />

            <div class="flex-1 md:max-w-2xl">
                <MerchantForm :model="merchant" :form-data="form" :categories="categories" @submit="handleSubmit" />
            </div>
        </MerchantLayout>
    </AdminLayout>
</template>
