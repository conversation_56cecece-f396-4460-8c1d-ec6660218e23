<script setup lang="ts">
import MerchantForm from '@/components/admin/merchants/MerchantForm.vue';
import Heading from '@/components/Heading.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, CheckboxItem, MerchantFormData, SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    categories: CheckboxItem[];
}

const { categories } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: t('Create new merchant'),
        href: route('admin.merchants.create', { currentMarket: currentMarket?.id }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantFormData>) => {
    form.post(route('admin.merchants.store', { currentMarket: currentMarket?.id }));
};
</script>

<template>
    <Head :title="t('Create new merchant')" />

    <AdminLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-6">
            <Heading :title="t('Create new merchant')" />

            <div class="flex-1 md:max-w-2xl">
                <MerchantForm :categories="categories" @submit="handleSubmit" />
            </div>
        </div>
    </AdminLayout>
</template>
