<script setup lang="ts">
import MerchantMenuCategoryForm from '@/components/admin/merchant-menu-categories/MerchantMenuCategoryForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, MerchantMenuCategoryFormData, SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    menuCategory: {
        id: number;
        name: string;
        description?: string;
        status: 'activated' | 'deactivated';
    };
}

const { merchant, menuCategory } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const formData: MerchantMenuCategoryFormData = {
    name: menuCategory.name,
    status: menuCategory.status,
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menu categories'),
        href: route('admin.merchants.menu-categories.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Edit menu category'),
        href: route('admin.merchants.menu-categories.edit', {
            currentMarket: currentMarket?.id,
            merchant: merchant.id,
            menu_category: menuCategory.id,
        }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuCategoryFormData>) => {
    form.put(
        route('admin.merchants.menu-categories.update', {
            currentMarket: currentMarket?.id,
            merchant: merchant.id,
            menu_category: menuCategory.id,
        }),
        {
            preserveScroll: true,
        },
    );
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Edit Menu Category')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuCategoryForm :form-data="formData" @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>

<style scoped></style>
