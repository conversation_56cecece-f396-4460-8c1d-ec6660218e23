<script setup lang="ts">
import MerchantMenuCategoryForm from '@/components/admin/merchant-menu-categories/MerchantMenuCategoryForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import type { BreadcrumbItem, Merchant, MerchantMenuCategoryFormData, SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
}

const { merchant } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menu categories'),
        href: route('admin.merchants.menu-categories.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Create menu category'),
        href: route('admin.merchants.menu-categories.create', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuCategoryFormData>) => {
    form.post(route('admin.merchants.menu-categories.store', { currentMarket: currentMarket?.id, merchant: merchant.id }), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Create Menu Category')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuCategoryForm @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>

<style scoped></style>
