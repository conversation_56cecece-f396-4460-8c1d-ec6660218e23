<script setup lang="ts">
import MerchantMenuItemForm from '@/components/admin/merchant-menu-items/MerchantMenuItemForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import { BreadcrumbItem, Merchant, MerchantMenuItem, MerchantMenuItemFormData, MerchantMenuModifierGroup, type SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    menu: MerchantMenuItem;
    modifierGroups: MerchantMenuModifierGroup[];
}

const { merchant, menu, modifierGroups } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const formData: MerchantMenuItemFormData = {
    name: menu.name,
    description: menu.description || '',
    price: menu.price,
    image: menu.image_path || '',
    image_url: menu.image_url || '',
    merchant_menu_category_id: menu.merchant_menu_category_id,
    status: menu.status,
    modifier_group_ids: menu.modifier_groups.map(function (modifierGroup: MerchantMenuModifierGroup) {
        return modifierGroup.id;
    }),
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menus'),
        href: route('admin.merchants.menus.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Edit menu'),
        href: route('admin.merchants.menus.edit', { currentMarket: currentMarket?.id, merchant: merchant.id, menu: menu.id }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuItemFormData>) => {
    form.post(route('admin.merchants.menus.update', { currentMarket: currentMarket?.id, merchant: merchant.id, menu: menu.id }), {
        preserveScroll: true,
    });
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Edit menu')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuItemForm :merchant="merchant" :form-data="formData" :modifier-groups="modifierGroups" @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>
