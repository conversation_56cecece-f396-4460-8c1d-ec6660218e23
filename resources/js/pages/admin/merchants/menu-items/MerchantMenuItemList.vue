<script setup lang="ts">
import DataTable from '@/components/admin/merchant-menu-items/DataTable.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import { BreadcrumbItem, Merchant, MerchantMenuItem, SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    menus: MerchantMenuItem[];
}

const { merchant } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menus'),
        href: route('admin.merchants.menus.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Menus')" />

        <MerchantLayout :merchant="merchant" layout="full">
            <div class="px-4 py-6" v-if="menus.length > 0">
                <DataTable :merchant="merchant" :merchant-menu-items="menus" />
            </div>
            <div class="px-4 py-6" v-else>
                <GettingStartedResourceCard :title="t('No merchant menus yet')" :description="t('Get started and create your first merchant menus.')">
                    <GettingStartedResourceAction
                        :href="route('admin.merchants.menus.create', { currentMarket: currentMarket?.id, merchant: merchant.id })"
                    >
                        {{ t('Create new menu') }}
                    </GettingStartedResourceAction>
                </GettingStartedResourceCard>
            </div>
        </MerchantLayout>
    </AdminLayout>
</template>
