<script setup lang="ts">
import MerchantMenuItemForm from '@/components/admin/merchant-menu-items/MerchantMenuItemForm.vue';
import AdminLayout from '@/layouts/AdminLayout.vue';
import MerchantLayout from '@/layouts/merchants/Layout.vue';
import { t } from '@/lib/utils';
import { BreadcrumbItem, Merchant, MerchantMenuItemFormData, MerchantMenuModifierGroup, type SharedData } from '@/types';
import { Head, InertiaForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    modifierGroups: MerchantMenuModifierGroup[];
}

const { merchant, modifierGroups } = defineProps<Props>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: t('Dashboard'),
        href: route('admin.index'),
    },
    {
        title: t('Merchants'),
        href: route('admin.merchants.index', { currentMarket: currentMarket?.id }),
    },
    {
        title: merchant.name,
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Menus'),
        href: route('admin.merchants.menus.index', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
    {
        title: t('Create new menu'),
        href: route('admin.merchants.menus.create', { currentMarket: currentMarket?.id, merchant: merchant.id }),
    },
];

const handleSubmit = (form: InertiaForm<MerchantMenuItemFormData>) => {
    form.post(route('admin.merchants.menus.store', { currentMarket: currentMarket?.id, merchant: merchant.id }), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <AdminLayout :breadcrumbs="breadcrumbs">
        <Head :title="t('Create new menu')" />

        <MerchantLayout :merchant="merchant">
            <MerchantMenuItemForm :merchant="merchant" :modifier-groups="modifierGroups" @submit="handleSubmit" />
        </MerchantLayout>
    </AdminLayout>
</template>
