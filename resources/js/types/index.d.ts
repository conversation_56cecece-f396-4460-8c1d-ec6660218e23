import type { PageProps } from '@inertiajs/core';
import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href?: string;
    route: string;
    icon?: LucideIcon;
    isActive?: boolean;
    children?: NavItem[];
    requiredMarket?: boolean;
}

export type Choice<T = { [key: string]: any }> = { id: string } & T;

export interface FlashMessage {
    success?: string;
    error?: string;
}

export interface SharedData extends PageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    flash: FlashMessage;
    currentRoute: string;
    currentMarket: Market | null;
    availableMarkets: Market[] | null;
}

export interface Model {
    id: string;
    created_at: string;
    updated_at: string;
}

export interface ModelWithoutTimestamps {
    id: string;
}

export interface User extends Model {
    name: string;
    email: string | null;
    avatar?: string;
    email_verified_at: string | null;
    phone_number: string;
    phone_number_verified_at: string | null;
}

export type PaginationLink = {
    url: string | null;
    label: string;
    active: boolean;
};

export type Pagination<T> = {
    data: T[];
    current_page: number;
    last_page: number;
    last_page_url: string;
    links: PaginationLink[];
    next_page_url: string | null;
    prev_page_url: string | null;
    per_page: number;
    total: number;
    from: number;
    to: number;
};

export type Account = User;

export type AccountFormData = {
    name: string;
    phone_number: string;
    email: string;
    password: string;
    password_confirmation: string;
};

export type ImageFormData = {
    id: string | null;
    file: File | null;
    path: string | null;
    url: string;
    priority?: number;
};

interface Market extends Model {
    name: string;
    account_id: string;
    account?: Account;
    description: string | null;
    opening_date: string;
    administrative_areas?: MarketAdministrativeArea[];
    status: 'pending' | 'activated' | 'deactivated';
}

interface MarketAdministrativeArea extends ModelWithoutTimestamps {
    market_id: string;
    market?: Market;
    administrative_area_level_1: string;
    administrative_area_level1?: AdministrativeAreaLevel1;
    administrative_area_level_2: string;
    administrative_area_level2?: AdministrativeAreaLevel2;
}

export type MarketAdministrativeArea1FormData = {
    id: string;
    name: string;
};

export type MarketAdministrativeArea2FormData = MarketAdministrativeArea1FormData;

export type MarketAdministrativeAreaFormData = {
    administrative_area_level_1: MarketAdministrativeArea1FormData;
    administrative_areas_level_2: MarketAdministrativeArea2FormData[];
};

export type MarketFormData = {
    name: string;
    description: string;
    opening_date: string;
    administrative_area: MarketAdministrativeAreaFormData;
    account_id: string;
    status: 'pending' | 'activated' | 'deactivated';
};

/**
 * @deprecated
 */
export type SelectOption = {
    label: string;
    value: string;
};

export type Customer = {
    id: string;
    name: string;
    email: string | null;
    email_verified_at: string | null;
    phone_number: string;
    phone_number_verified_at: string | null;
    referral_code: string;
    referred_by: string | null;
    created_at: string;
    updated_at: string;
};

export type CustomerFormData = {
    name: string;
    email: string;
    phone_number: string;
    password: string;
    password_confirmation: string;
};

export interface TimePeriod {
    start: string;
    end: string;
}

export type DayOfWeek = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';

export type BusinessHours = {
    name: string;
    started_at: string | null;
    ended_at: string | null;
    hours: Record<
        DayOfWeek,
        {
            periods: TimePeriod[];
        } | null
    >;
};

export type BusinessHoursFormData = BusinessHours;

export interface Merchant extends Model {
    name: string;
    market_id: string;
    market?: Market;
    account_id: string;
    account?: Account;
    address_component_id: string;
    address_component?: AddressComponent;
    contact_email: string | null;
    contact_phone_number: string;
    description: string | null;
    status: 'activated' | 'deactivated' | 'pending';
    rating: number;
    created_at: string;
    updated_at: string;
    categories: MerchantCategory[];
    images?: MerchantImage[];
    business_hours?: BusinessHours[];
}

export type SellingTimeFormData = {
    selling_times: BusinessHours[];
};

export interface AddressComponent extends Model {
    name: string;
    place_id: string | null;
    formatted_address: string;
    address: string;
    administrative_area_level_3: string | null;
    administrative_area_level_2: string | null;
    administrative_area_level_1: string | null;
    country: string | null;
    postal_code: string | null;
    latitude: number | null;
    longitude: number | null;
    source: 'google' | 'manual';
}

export type AddressComponentFormData = {
    name: string;
    place_id: string | null;
    formatted_address: string;
    address: string;
    administrative_area_level_3: string | null;
    administrative_area_level_2: string | null;
    administrative_area_level_1: string | null;
    country: string | null;
    postal_code: string | null;
    latitude: number | null;
    longitude: number | null;
    source: 'google' | 'manual';
};

export interface Country extends ModelWithoutTimestamps {
    address_component_id: string;
    address_component?: AddressComponent;
    name: string;
    code: string;
}

export interface AdministrativeAreaLevel1 extends ModelWithoutTimestamps {
    address_component_id: string;
    address_component?: AddressComponent;
    country_id: string;
    country?: Country;
    name: string;
}

export interface AdministrativeAreaLevel2 extends ModelWithoutTimestamps {
    address_component_id: string;
    address_component?: AddressComponent;
    country_id: string;
    country?: Country;
    administrative_area_level_1: string;
    administrative_area_level1?: AdministrativeAreaLevel1;
    name: string;
}

export interface MerchantCategory extends Model {
    id: string;
    name: string;
    image_path: string | null;
    image_url: string | null;
    priority: number;
}

export interface MerchantImage extends Model {
    id: string;
    image_path: string | null;
    image_url: string | null;
    priority: number;
}

export type MerchantFormData = {
    name: string;
    account_id: string;
    address_component: AddressComponentFormData;
    category_ids: string[];
    contact_email: string;
    contact_phone_number: string;
    description: string;
    status: 'activated' | 'deactivated' | 'pending';
    images: ImageFormData[];
};

export interface MerchantCategory extends Model {
    name: string;
    image_path: string | null;
    image_url: string | null;
}

export type MerchantCategoryFormData = {
    name: string;
    image: File | null;
    image_path: string | null;
};

export type MerchantMenuItemFormData = {
    name: string;
    description: string;
    price: number;
    image: File | string | null;
    image_url?: string;
    merchant_menu_category_id: string;
    status: string;
    modifier_group_ids: string[];
};

export interface MerchantMenuCategory extends Model {
    name: string;
    market_id: string;
    merchant_id: string;
    status: 'activated' | 'deactivated';
}

export type MerchantMenuCategoryFormData = {
    name: string;
    status: 'activated' | 'deactivated';
};

export type MerchantMenuModifierGroupFormData = {
    name: string;
    selection_min: number;
    selection_max: number;
    priority: number;
    status: 'activated' | 'deactivated';
    modifier_items: MerchantMenuModifierItemFormData[];
};

export type MerchantMenuModifierItemFormData = {
    name: string;
    price: number;
    status: 'activated' | 'deactivated';
};

export interface MerchantMenuModifierGroup extends Model {
    name: string;
    market_id: string;
    merchant_id: string;
    selection_min: number;
    selection_max: number;
    status: 'activated' | 'deactivated';
}

export interface MerchantMenuModifierItem extends Model {
    name: string;
    price: number;
    status: 'activated' | 'deactivated';
}

export interface MerchantMenuItem extends Model {
    name: string;
    market_id: string;
    merchant_id: string;
    description?: string;
    price: number;
    image_path?: string;
    image_url?: string;
    merchant_menu_category_id: string;
    status: 'activated' | 'deactivated' | 'pending';
    modifier_group_ids: string[];
    modifier_groups: MerchantMenuModifierGroup[];
}

export interface CheckboxItem {
    id: string | number;
    label: string;
    value: string | number;
    disabled?: boolean;
}

export interface CheckboxListProps {
    items: CheckboxItem[];
    modelValue: (string | number)[];
    name?: string;
    disabled?: boolean;
}
