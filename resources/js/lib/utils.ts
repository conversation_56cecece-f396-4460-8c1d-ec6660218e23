import type { SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';
import vietnameseTranslations from '@lang/vi.json';
import axios from 'axios';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

const locale = 'vi';

const translations = {
    vi: vietnameseTranslations,
};

export function t(key: string, replacements?: { [key: string]: string | number }) {
    let translation = translations[locale][key] || key;

    if (replacements) {
        Object.keys(replacements).forEach((k) => {
            translation = translation.replace(`:${k}`, replacements[k].toString());
        });
    }

    return translation;
}

export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
    let timeout: ReturnType<typeof setTimeout> | null;

    return function (...args: Parameters<T>) {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => {
            func(...args);
        }, wait);
    };
}

export const http = axios.create({
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
    },
    withCredentials: true,
});

export function isRouteActive(route: string) {
    if (!route) return false;

    const page = usePage<SharedData>();
    const currentRoute = page.props.currentRoute;

    if (route.endsWith('.index')) {
        const baseRoute = route.slice(0, -6);
        return currentRoute.startsWith(baseRoute);
    }

    return currentRoute === route;
}
