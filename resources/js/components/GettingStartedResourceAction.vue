<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Method } from '@inertiajs/core';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

defineProps<{
    href:
        | string
        | {
              url: string;
              method: Method;
          };
}>();
</script>

<template>
    <Link :href="href" class="text-center">
        <Button>
            <slot name="icon">
                <CirclePlus :size="16" />
            </slot>

            <slot />
        </Button>
    </Link>
</template>
