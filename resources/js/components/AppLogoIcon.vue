<script setup lang="ts">
import type { HTMLAttributes } from 'vue';

defineOptions({
    inheritAttrs: false,
});

interface Props {
    className?: HTMLAttributes['class'];
}

defineProps<Props>();
</script>

<template>
    <svg :class="className" v-bind="$attrs" viewBox="0 0 775 375" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            class="text-logo"
            d="M136.138 101.543C106.762 101.543 78.2419 110.338 58.6814 125.02V31.1581C58.6814 14.9515 45.5473 1.81738 29.3407 1.81738C13.1341 1.81738 0 14.9515 0 31.1581V238.091C0 312.967 59.7954 374.639 136.149 374.639C212.503 374.639 272.685 312.979 272.685 238.091C272.685 163.203 211.002 101.555 136.126 101.555L136.138 101.543ZM135.762 314.445C93.1819 314.445 57.9309 280.671 57.9309 238.079C57.9309 195.487 93.1819 161.725 135.762 161.725C178.343 161.725 212.104 195.499 212.104 238.079C212.104 280.66 178.343 314.445 135.762 314.445Z"
            fill="currentColor"
        />
        <path
            d="M290.242 237.27C290.242 207.648 299.612 179.515 317.331 155.909L322.339 149.483C342.72 125.396 371.357 108.509 403.888 103.197C411.076 102.012 418.452 101.402 425.969 101.402C433.486 101.402 440.851 102.012 448.028 103.185C480.488 108.485 509.078 125.314 529.459 149.33L534.584 155.909C552.303 179.527 561.685 207.648 561.685 237.27C561.685 280.191 541.022 321.059 506.416 346.565C483.091 363.757 454.911 372.751 425.969 372.833C421.056 372.81 416.177 372.552 411.322 372.012C437.52 368.142 461.185 354.445 477.79 333.138L504.997 298.238C518.319 280.929 525.836 259.469 525.836 237.247C525.836 215.024 519.011 194.948 506.099 177.651L503.93 174.871C501.666 171.963 499.262 169.149 496.647 166.534L496.589 166.475C477.72 147.606 452.648 137.216 425.969 137.216C399.291 137.216 374.207 147.606 355.35 166.475C353.567 168.246 351.867 170.087 350.237 171.963L345.816 177.627C332.905 194.936 326.08 215.54 326.08 237.235C326.08 258.93 333.385 280.296 346.344 297.476C346.989 298.332 347.645 299.165 348.314 300.009C353.532 306.482 359.583 312.334 366.361 317.388C366.361 317.388 366.526 317.494 366.573 317.517C390.249 331.93 421.232 326.84 439.08 305.474L439.502 304.981L466.415 270.469C466.474 270.387 467.752 268.757 467.776 268.722C474.577 259.657 478.342 248.469 478.342 237.247V237.012C478.342 226.986 475.504 217.112 469.933 208.762C469.933 208.762 468.808 207.085 468.28 206.37C468.28 206.37 467.565 205.455 466.814 204.482C465.606 202.946 464.328 201.456 462.979 200.061C462.675 199.756 462.358 199.439 462.041 199.146C461.185 198.313 460.306 197.528 459.391 196.777C459.203 196.613 459.016 196.461 458.816 196.296C458.265 195.839 457.104 194.971 457.092 194.96C456.741 194.702 456.389 194.444 456.049 194.209C455.005 193.459 453.926 192.767 452.836 192.11C450.197 190.515 447.418 189.166 444.533 188.088C443.829 187.818 443.138 187.572 442.422 187.349C437.18 185.613 431.645 184.71 425.958 184.71C411.967 184.71 398.81 190.163 388.901 200.072C387.54 201.515 386.25 203.016 385.031 204.576L383.635 206.37C383.108 207.085 382.592 207.824 382.111 208.563C376.517 217.053 373.585 226.915 373.585 237.235C373.585 248.505 377.373 259.739 384.233 268.816C384.409 269.05 384.585 269.273 384.761 269.507L415.802 309.332C409.669 312.393 402.82 314.023 395.737 314.023C395.163 314.023 394.588 314.023 394.014 313.988C383.518 313.589 373.644 309.051 365.951 301.885C362.89 299.036 360.017 295.963 357.379 292.68C356.781 291.941 356.194 291.179 355.643 290.452C344.092 275.136 337.736 256.244 337.736 237.235C337.736 218.226 343.729 199.943 355.08 184.687L356.98 182.248C359.02 179.632 361.178 177.099 363.535 174.742L363.582 174.695C366.725 171.553 370.067 168.68 373.574 166.064C388.654 154.889 406.843 148.849 425.969 148.849C445.096 148.849 463.261 154.877 478.342 166.076C481.86 168.68 485.214 171.564 488.368 174.719C489.717 176.068 491.042 177.475 492.449 179.07L496.823 184.687C503.308 193.412 508.058 203.098 510.931 213.359C513.077 221.04 514.179 229.038 514.179 237.223C514.179 257.276 507.237 276.637 494.97 292.117L468.597 325.949C467.94 326.793 467.271 327.626 466.58 328.435C436.054 364.988 384.104 371.215 346.285 347.104L346.226 347.057C337.678 340.842 329.973 333.7 323.207 325.832C302.215 301.405 290.242 269.918 290.242 237.235V237.27Z"
            fill="#00CE68"
        />
        <path
            class="text-logo"
            d="M425.957 262.296C439.862 262.296 451.135 251.023 451.135 237.118C451.135 223.213 439.862 211.94 425.957 211.94C412.052 211.94 400.779 223.213 400.779 237.118C400.779 251.023 412.052 262.296 425.957 262.296Z"
            fill="currentColor"
        />
        <path
            class="text-logo"
            d="M608.415 0H608.403C592.289 0 579.227 13.0628 579.227 29.1765V343.434C579.227 359.548 592.289 372.61 608.403 372.61H608.415C624.529 372.61 637.591 359.548 637.591 343.434V29.1765C637.591 13.0628 624.529 0 608.415 0Z"
            fill="currentColor"
        />
        <path
            class="text-logo"
            d="M743.756 373.689C734.281 373.689 724.935 369.315 718.919 361.071L643.48 257.663C635.74 247.05 635.599 232.696 643.14 221.943L707.321 130.356C717.054 116.471 736.204 113.094 750.089 122.827C763.974 132.561 767.351 151.711 757.618 165.595L706.043 239.205L768.535 324.87C778.527 338.567 775.524 357.787 761.827 367.779C756.363 371.766 750.03 373.677 743.756 373.677V373.689Z"
            fill="currentColor"
        />
    </svg>
</template>
