<script setup lang="ts">
import { InertiaLinkProps, Link } from '@inertiajs/vue3';

interface Props extends InertiaLinkProps {
    tabindex?: number;
}

defineProps<Props>();
</script>

<template>
    <Link
        v-bind="$props"
        :tabindex="tabindex"
        class="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
    >
        <slot />
    </Link>
</template>
