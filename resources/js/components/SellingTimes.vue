<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { TimePicker } from '@/components/ui/time-picker';
import { t } from '@/lib/utils';
import type { BusinessHours, TimePeriod } from '@/types';
import { PlusIcon, TrashIcon } from 'lucide-vue-next';
import { ref } from 'vue';

interface Props {
    items: BusinessHours[];
    onChange?: (items: BusinessHours[]) => void;
    isEditable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    isEditable: false,
});

const DAYS_OF_WEEK = [t('Monday'), t('Tuesday'), t('Wednesday'), t('Thursday'), t('Friday'), t('Saturday'), t('Sunday')];

const MAX_CLUSTERS = 3;

const sellingTimes = ref<BusinessHours[]>(
    props.items.length
        ? props.items
        : DAYS_OF_WEEK.map((day) => ({
              day,
              is_open: false,
              is_full: false,
              clusters: {},
          })),
);

const handleAddCluster = (dayIndex: number) => {
    if (!props.isEditable) return;

    const newTimes = [...sellingTimes.value];
    const currentClusters = newTimes[dayIndex].clusters;
    const clusterCount = Object.keys(currentClusters).length;

    if (clusterCount >= MAX_CLUSTERS) {
        return;
    }

    let newCluster: TimePeriod;
    switch (clusterCount) {
        case 0:
            newCluster = { start: '08:00', end: '12:00' };
            break;
        case 1:
            newCluster = { start: '12:00', end: '17:00' };
            break;
        case 2:
            newCluster = { start: '17:00', end: '23:00' };
            break;
        default:
            newCluster = { start: '08:00', end: '17:00' };
    }

    newTimes[dayIndex].clusters = {
        ...currentClusters,
        [clusterCount]: newCluster,
    };

    sellingTimes.value = newTimes;
    props.onChange?.(newTimes);
};

const handleRemoveCluster = (dayIndex: number, clusterIndex: number) => {
    if (!props.isEditable) return;

    const newTimes = [...sellingTimes.value];
    const currentClusters = { ...newTimes[dayIndex].clusters };
    delete currentClusters[clusterIndex];

    const reIndexedClusters: { [key: string]: TimePeriod } = {};
    Object.values(currentClusters).forEach((cluster, index) => {
        reIndexedClusters[index] = cluster;
    });

    newTimes[dayIndex].clusters = reIndexedClusters;
    sellingTimes.value = newTimes;
    props.onChange?.(newTimes);
};

const handleTimeChange = (dayIndex: number, clusterIndex: number, type: 'start' | 'end', value: string) => {
    if (!props.isEditable) return;

    const newTimes = [...sellingTimes.value];
    const updatedCluster = {
        ...newTimes[dayIndex].clusters[clusterIndex],
        [type]: value,
    };

    newTimes[dayIndex].clusters = {
        ...newTimes[dayIndex].clusters,
        [clusterIndex]: updatedCluster,
    };

    sellingTimes.value = newTimes;
    props.onChange?.(newTimes);
};

const handleFullDayChange = (dayIndex: number, value: boolean) => {
    if (!props.isEditable) return;

    const newTimes = [...sellingTimes.value];
    newTimes[dayIndex].is_full = value;

    if (value) {
        newTimes[dayIndex].clusters = {};
    }

    sellingTimes.value = newTimes;
    props.onChange?.(newTimes);
};
</script>

<template>
    <div class="space-y-3">
        <div v-for="(item, dayIndex) in sellingTimes" :key="item.day" class="border-border dark:border-border-dark overflow-hidden rounded-lg border">
            <div class="flex items-center justify-between p-4" :class="{ 'border-border dark:border-border-dark border-b': item.is_open }">
                <div class="flex w-full items-center justify-between gap-3">
                    <span class="text-foreground dark:text-foreground-dark font-medium">{{ t(item.day) }}</span>
                    <div v-if="isEditable" class="flex items-center gap-2">
                        <Switch v-model="item.is_open" />
                        <span class="text-muted-foreground dark:text-muted-foreground-dark text-sm">
                            {{ t('Open') }}
                        </span>
                    </div>
                </div>

                <span
                    v-if="!isEditable && !item.is_open"
                    class="shrink-0 rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-700 dark:bg-red-900/30 dark:text-red-400"
                >
                    {{ t('Closed') }}
                </span>
            </div>

            <div v-if="isEditable && item.is_open" class="border-border dark:border-border-dark border-b px-4 py-3">
                <div class="flex items-center gap-2">
                    <Switch v-model="item.is_full" @update:modelValue="(value) => handleFullDayChange(dayIndex, value)" />
                    <span class="text-muted-foreground dark:text-muted-foreground-dark text-sm">
                        {{ t('Full day') }}
                    </span>
                </div>
            </div>

            <div v-if="item.is_open && !item.is_full" class="p-4 pt-3">
                <div class="space-y-3">
                    <template v-if="isEditable">
                        <div
                            v-for="(cluster, clusterIndex) in Object.entries(item.clusters)"
                            :key="clusterIndex"
                            class="border-border dark:border-border-dark rounded-md border p-3"
                        >
                            <div class="mb-2 flex items-center justify-between">
                                <span class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium">
                                    {{ t('Time Slot') }} {{ Number(clusterIndex) + 1 }}
                                </span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    class="text-destructive hover:bg-destructive/10 hover:text-destructive dark:text-destructive-dark dark:hover:bg-destructive-dark/10 dark:hover:text-destructive-dark h-7 px-2"
                                    @click="handleRemoveCluster(dayIndex, Number(clusterIndex))"
                                >
                                    <TrashIcon class="mr-1 h-3.5 w-3.5" />
                                    {{ t('Delete') }}
                                </Button>
                            </div>

                            <div class="flex items-center gap-2">
                                <div class="grid gap-1.5">
                                    <label
                                        :for="`start-${dayIndex}-${clusterIndex}`"
                                        class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium"
                                    >
                                        {{ t('Start') }}
                                    </label>
                                    <TimePicker
                                        :id="`start-${dayIndex}-${clusterIndex}`"
                                        :modelValue="cluster[1].start"
                                        @update:modelValue="(value: string) => handleTimeChange(dayIndex, Number(clusterIndex), 'start', value)"
                                    />
                                </div>
                                <div class="text-muted-foreground dark:text-muted-foreground-dark mt-6">{{ t('to') }}</div>
                                <div class="grid gap-1.5">
                                    <label
                                        :for="`end-${dayIndex}-${clusterIndex}`"
                                        class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium"
                                    >
                                        {{ t('End') }}
                                    </label>
                                    <TimePicker
                                        :id="`end-${dayIndex}-${clusterIndex}`"
                                        :modelValue="cluster[1].end"
                                        @update:modelValue="(value: string) => handleTimeChange(dayIndex, Number(clusterIndex), 'end', value)"
                                    />
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="flex flex-wrap items-center justify-end gap-2">
                            <span
                                v-for="(cluster, clusterIndex) in Object.entries(item.clusters)"
                                :key="clusterIndex"
                                class="rounded-lg bg-green-100 px-2 py-1 text-xs font-bold whitespace-nowrap text-green-700 dark:bg-green-900/30 dark:text-green-400"
                            >
                                {{ cluster[1].start }} - {{ cluster[1].end }}
                            </span>
                        </div>
                    </template>

                    <Button
                        v-if="isEditable && Object.keys(item.clusters).length < MAX_CLUSTERS"
                        variant="outline"
                        class="mt-2 w-full justify-center"
                        @click="handleAddCluster(dayIndex)"
                    >
                        <PlusIcon class="mr-1 h-4 w-4" />
                        {{ t('Add Time Slot') }}
                    </Button>

                    <div
                        v-if="!isEditable && Object.keys(item.clusters).length === 0"
                        class="text-muted-foreground dark:text-muted-foreground-dark py-2 text-center text-sm"
                    >
                        {{ t('No time slots set') }}
                    </div>
                </div>
            </div>
            <div v-if="item.is_open && item.is_full" class="p-4 pt-3">
                <div class="text-muted-foreground dark:text-muted-foreground-dark text-center text-sm">
                    {{ t('Open all day') }}
                </div>
            </div>
        </div>
    </div>
</template>
