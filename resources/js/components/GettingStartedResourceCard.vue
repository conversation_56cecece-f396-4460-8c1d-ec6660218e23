<script lang="ts" setup>
import { Card, CardContent } from '@/components/ui/card';
import { Inbox } from 'lucide-vue-next';

interface Props {
    title: string;
    description?: string;
}

defineProps<Props>();
</script>
<template>
    <Card>
        <CardContent class="flex flex-col items-center justify-center gap-2">
            <slot name="icon">
                <Inbox :size="48" class="text-muted-foreground mb-4" />
            </slot>

            <h3 class="font-medium">{{ title }}</h3>

            <p class="text-muted-foreground mb-2 text-sm" v-if="description">{{ description }}</p>

            <div class="flex items-center justify-center gap-2">
                <slot />
            </div>
        </CardContent>
    </Card>
</template>
