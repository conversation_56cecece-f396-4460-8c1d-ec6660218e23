<script setup lang="ts">
import type { CheckboxListProps, CheckboxItem } from '@/types';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const props = withDefaults(defineProps<CheckboxListProps>(), {
    name: '',
    disabled: false
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: (string | number)[]): void;
}>();

const handleChange = (item: CheckboxItem) => {
    if (props.disabled || item.disabled) return;

    const newValue = [...props.modelValue];
    const index = newValue.indexOf(item.value);

    if (index === -1) {
        newValue.push(item.value);
    } else {
        newValue.splice(index, 1);
    }

    emit('update:modelValue', newValue);
};

const isChecked = (item: CheckboxItem) => {
    return props.modelValue.includes(item.value);
};
</script>

<template>
    <div class="w-full" v-if="items.length > 0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <label
                v-for="item in items"
                :key="item.value"
                class="flex items-center space-x-2 cursor-pointer"
                :class="{ 'opacity-50 cursor-not-allowed': disabled || item.disabled }"
            >
                <Checkbox
                    :name="name"
                    :value="item.value"
                    :checked="isChecked(item)"
                    :disabled="disabled || item.disabled"
                    @change="handleChange(item)"
                    class="w-4 h-4"
                    :id="`checkbox-list-item-${item.value}`"
                />
                <Label :for="`checkbox-list-item-${item.value}`">{{ item.label }}</Label>
            </label>
        </div>
    </div>
</template>
