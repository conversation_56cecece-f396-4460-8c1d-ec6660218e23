<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { ref, nextTick, onMounted, useTemplateRef } from 'vue';
import { X, RotateCcw } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { ImageFormData } from '@/types';

interface Props {
    modelValue: ImageFormData[];
    aspectRatio?: number;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    aspectRatio: 16 / 9,
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: ImageFormData[]): void;
    (e: 'restore'): void;
    (e: 'remove'): void;
}>();

const inputRef = useTemplateRef<{ inputRef: HTMLInputElement | null } | null>('inputRef');

const changeHandler = async (event: Event) => {
    const target = event.target as HTMLInputElement;

    if (!target.files || target.files.length === 0) {
        return;
    }

    const value: ImageFormData[] = [...props.modelValue];

    if (target.files && target.files.length > 0) {
        for (const file of target.files) {
            value.push({
                id: null,
                file: file,
                path: null,
                url: URL.createObjectURL(file),
                priority: 0,
            });
        }
    }

    emit('update:modelValue', value);

    await nextTick();

    if (inputRef.value && inputRef.value.inputRef) {
        inputRef.value.inputRef.value = '';
    }
}

const removeHandler = async (index: number) => {
    const value = [...props.modelValue];

    value.splice(index, 1);

    emit('update:modelValue', value);

    emit('remove');

    await nextTick();

    if (inputRef.value && inputRef.value.inputRef) {
        inputRef.value.inputRef.value = '';
    }
}
</script>

<template>
    <Input ref="inputRef" v-bind="$attrs" type="file" accept="image/*" @change="changeHandler" multiple />

    <div v-if="modelValue && modelValue.length > 0" class="relative grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
        <div v-for="(image, index) in modelValue" :key="index" class="relative border-1 border-solid border-input/80 rounded-md">
            <AspectRatio :ratio="aspectRatio">
                <img
                    :src="image.url"
                    alt="Preview"
                    class="rounded-md object-cover w-full h-full"
                />
            </AspectRatio>

            <div class="absolute top-3 right-3 space-x-1">
                <Button
                    @click="removeHandler(index)"
                    variant="destructive"
                    class="rounded-full p-0 cursor-pointer"
                    type="button"
                >
                    <X class="h-4 w-4" />
                </Button>
            </div>
        </div>
    </div>
</template>
