<script setup lang="ts">
import { ref } from 'vue'

interface Props {
    modelValue?: string
    id?: string
}

defineProps<Props>()
defineEmits<{
    'update:modelValue': [value: string]
}>()

const inputRef = ref<HTMLInputElement | null>(null)

const handleIconClick = () => {
    inputRef.value?.showPicker()
}
</script>

<template>
  <div class="relative">
    <input
      ref="inputRef"
      type="time"
      :id="id"
      :value="modelValue"
      @input="(e: Event) => {
        const target = e.target as HTMLInputElement;
        if (target) {
          $emit('update:modelValue', target.value);
        }
      }"
      class="w-32 font-mono appearance-none dark:bg-gray-800 dark:text-white dark:border-gray-700 pr-8 [&::-webkit-calendar-picker-indicator]:hidden"
      v-bind="$attrs"
    />
    <button
      type="button"
      @click="handleIconClick"
      class="absolute right-2 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="h-4 w-4 text-gray-500 dark:text-gray-400"
      >
        <circle cx="12" cy="12" r="10" />
        <polyline points="12 6 12 12 16 14" />
      </svg>
    </button>
  </div>
</template>
