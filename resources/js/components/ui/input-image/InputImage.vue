<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { ref, nextTick, useTemplateRef } from 'vue';
import { X, RotateCcw } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface Props {
    modelValue: File | null;
    previewUrl?: string | null;
    aspectRatio?: number;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    previewUrl: '',
    aspectRatio: 16 / 9,
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: File | null): void;
    (e: 'restore'): void;
    (e: 'remove'): void;
}>();

const inputRef = useTemplateRef<{ inputRef: HTMLInputElement | null } | null>('inputRef');
const url = ref<string | null>(props.previewUrl);

const changeHandler = (event: Event) => {
    const target = event.target as HTMLInputElement;

    if (target.files && target.files.length > 0) {
        const file = target.files[0];

        url.value = URL.createObjectURL(file)

        emit('update:modelValue', file);
    }
}

const restoreHandler = async () => {
    url.value = props.previewUrl;

    emit('update:modelValue', null);

    emit('restore');

    await nextTick();

    if (inputRef.value?.inputRef) {
        inputRef.value.inputRef.value = '';
    }
}

const removeHandler = async () => {
    url.value = '';

    emit('update:modelValue', null);

    emit('remove');

    await nextTick();

    if (inputRef.value?.inputRef) {
        inputRef.value.inputRef.value = '';
    }
}
</script>

<template>
    <Input ref="inputRef" v-bind="$attrs" type="file" accept="image/*" @change="changeHandler" />

    <div v-if="url" class="relative group mt-1">
        <div class="relative border-1 border-solid border-input/80 rounded-md">
            <AspectRatio :ratio="aspectRatio">
                <img
                    :src="url"
                    alt="Preview"
                    class="rounded-md object-cover w-full h-full"
                />
            </AspectRatio>

            <div class="absolute top-3 right-3 space-x-1">
                <Button
                    @click="restoreHandler"
                    variant="outline"
                    class="rounded-full p-0 cursor-pointer"
                    type="button"
                    v-if="previewUrl && modelValue"
                >
                    <RotateCcw class="h-4 w-4" />
                </Button>

                <Button
                    @click="removeHandler"
                    variant="destructive"
                    class="rounded-full p-0 cursor-pointer"
                    type="button"
                >
                    <X class="h-4 w-4" />
                </Button>
            </div>
        </div>
    </div>
</template>
