<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { isRouteActive } from '@/lib/utils';
import type { NavGroup, SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { ChevronDown } from 'lucide-vue-next';
import { onMounted, ref, watch } from 'vue';

const props = defineProps<{
    groupItems: NavGroup[];
}>();

const page = usePage<SharedData>();
const { currentMarket } = page.props;
const expandedMenus = ref<Set<string>>(new Set());

const toggleMenu = (title: string) => {
    if (expandedMenus.value.has(title)) {
        expandedMenus.value.delete(title);
    } else {
        expandedMenus.value.add(title);
    }
};

const checkAndExpandActiveMenus = () => {
    props.groupItems.forEach((group: NavGroup) => {
        group.items.forEach((item) => {
            if (isRouteActive(item.route)) {
                expandedMenus.value.add(item.title);
            }

            if (item.children) {
                const hasActiveChild = item.children.some((child) => isRouteActive(child.route));
                if (hasActiveChild) {
                    expandedMenus.value.add(item.title);
                }
            }
        });
    });
};

watch(
    () => page.url,
    () => {
        checkAndExpandActiveMenus();
    },
);

onMounted(() => {
    checkAndExpandActiveMenus();
});
</script>

<template>
    <SidebarGroup v-for="group in groupItems" :key="group.title" class="px-2 py-0">
        <SidebarGroupLabel>{{ group.title }}</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem v-for="item in group.items" :key="item.title">
                <template v-if="item.children">
                    <SidebarMenuButton
                        :is-active="isRouteActive(item.route) || item.children.some((child) => isRouteActive(child.route))"
                        :tooltip="item.title"
                        @click="toggleMenu(item.title)"
                        v-if="!item.requiredMarket || currentMarket"
                    >
                        <component :is="item.icon" />
                        <span>{{ item.title }}</span>
                        <ChevronDown class="ml-auto h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.has(item.title) }" />
                    </SidebarMenuButton>

                    <SidebarMenuButton v-else type="button" class="text-muted-foreground cursor-not-allowed">
                        <component :is="item.icon" />
                        <span>{{ item.title }}</span>
                    </SidebarMenuButton>

                    <div v-if="(!item.requiredMarket || currentMarket) && expandedMenus.has(item.title)" class="mt-1 ml-4">
                        <SidebarMenuItem v-for="child in item.children" :key="child.title">
                            <SidebarMenuButton as-child :is-active="isRouteActive(child.route)" :tooltip="child.title">
                                <Link :href="child.requiredMarket ? route(child.route, { currentMarket: currentMarket?.id }) : route(child.route)">
                                    <component :is="child.icon" />
                                    <span>{{ child.title }}</span>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </div>
                </template>
                <template v-else>
                    <SidebarMenuButton
                        as-child
                        :is-active="isRouteActive(item.route)"
                        :tooltip="item.title"
                        v-if="!item.requiredMarket || currentMarket"
                    >
                        <Link :href="item.requiredMarket ? route(item.route, { currentMarket: currentMarket?.id }) : route(item.route)">
                            <component :is="item.icon" />
                            <span>{{ item.title }}</span>
                        </Link>
                    </SidebarMenuButton>
                    <SidebarMenuButton v-else type="button" class="text-muted-foreground cursor-not-allowed">
                        <component :is="item.icon" />
                        <span>{{ item.title }}</span>
                    </SidebarMenuButton>
                </template>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>
</template>
