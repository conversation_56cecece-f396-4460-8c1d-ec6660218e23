<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { t } from '@/lib/utils';
import { MerchantMenuCategoryFormData } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';
interface Props {
    formData?: MerchantMenuCategoryFormData;
}

const props = withDefaults(defineProps<Props>(), {
    formData: () => ({
        name: '',
        status: 'activated',
    }),
});

const emit = defineEmits<{
    submit: [form: InertiaForm<MerchantMenuCategoryFormData>];
}>();

const form = useForm<MerchantMenuCategoryFormData>(props.formData);

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="space-y-6">
            <div class="grid gap-2">
                <Label for="name">{{ t('Name') }}</Label>
                <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" />
                <InputError :message="form.errors.name" />
            </div>

            <div class="grid gap-2">
                <Label for="status">{{ t('Status') }}</Label>
                <Select v-model="form.status">
                    <SelectTrigger class="w-full">
                        <SelectValue :placeholder="t('Select status')" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                        <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                    </SelectContent>
                </Select>
                <InputError :message="form.errors.status" />
            </div>
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
