<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/merchant-menu-modifier-groups/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Merchant, MerchantMenuModifierGroup } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    merchantMenuModifierGroups: MerchantMenuModifierGroup[];
    merchant: Merchant;
}

const { merchantMenuModifierGroups, merchant } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="merchantMenuModifierGroups" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.merchants.menu-modifier-groups.create', { currentMarket: merchant.market_id, merchant: merchant.id })">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
