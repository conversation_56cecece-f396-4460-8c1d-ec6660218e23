<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { t } from '@/lib/utils';
import type { MerchantMenuModifierGroupFormData, MerchantMenuModifierItemFormData } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';
import { Trash2 } from 'lucide-vue-next';

interface Props {
    formData?: MerchantMenuModifierGroupFormData;
}

const props = withDefaults(defineProps<Props>(), {
    formData: () => ({
        name: '',
        selection_min: 0,
        selection_max: 0,
        priority: 0,
        status: 'activated',
        modifier_items: [] as MerchantMenuModifierItemFormData[],
    }),
});

const emit = defineEmits<{
    submit: [form: InertiaForm<MerchantMenuModifierGroupFormData>];
}>();

const form = useForm<MerchantMenuModifierGroupFormData>(props.formData);

const addMenuModifierItem = () => {
    const newItem: MerchantMenuModifierItemFormData = {
        name: '',
        price: 0,
        status: 'activated' as const,
    };
    form.modifier_items = [...form.modifier_items, newItem];
};

const removeMenuModifierItem = (index: number) => {
    form.modifier_items = form.modifier_items.filter((_, i) => i !== index);
};

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="space-y-6">
            <div class="grid gap-2">
                <Label for="name">{{ t('Name') }}</Label>
                <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" />
                <InputError :message="form.errors.name" />
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="grid gap-2">
                    <Label for="selection_min">{{ t('Minimum selection') }}</Label>
                    <Input id="selection_min" v-model="form.selection_min" type="number" class="mt-1 block w-full" />
                    <InputError :message="form.errors.selection_min" />
                </div>

                <div class="grid gap-2">
                    <Label for="selection_max">{{ t('Maximum selection') }}</Label>
                    <Input id="selection_max" v-model="form.selection_max" type="number" class="mt-1 block w-full" />
                    <InputError :message="form.errors.selection_max" />
                </div>
            </div>

            <div class="grid gap-2">
                <Label for="status">{{ t('Status') }}</Label>
                <Select v-model="form.status">
                    <SelectTrigger class="w-full">
                        <SelectValue :placeholder="t('Select status')" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                        <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                    </SelectContent>
                </Select>
                <InputError :message="form.errors.status" />
            </div>

            <div class="space-y-4">
                <Label>{{ t('Options') }}</Label>
                <div class="space-y-4">
                    <div v-for="(item, index) in form.modifier_items" :key="index" class="relative flex flex-col gap-4 rounded-md border p-4">
                        <div class="flex items-center justify-between">
                            <div class="text-sm font-medium">{{ t('Option') }} #{{ index + 1 }}</div>
                            <Button size="sm" variant="destructive" @click="removeMenuModifierItem(index)">
                                <Trash2 class="h-4 w-4" />
                            </Button>
                        </div>

                        <div class="grid grid-cols-1 items-start gap-4 md:grid-cols-2">
                            <div class="grid gap-2">
                                <Label :for="`modifier_item_name_${index}`">{{ t('Name') }}</Label>
                                <Input :id="`modifier_item_name_${index}`" v-model="form.modifier_items[index].name" type="text" />
                                <InputError :message="(form.errors as Record<string, string>)[`modifier_items.${index}.name`] || ''" />
                            </div>

                            <div class="grid gap-2">
                                <Label :for="`modifier_item_price_${index}`">{{ t('Price') }}</Label>
                                <Input :id="`modifier_item_price_${index}`" v-model.number="form.modifier_items[index].price" type="number" min="0" />
                                <InputError :message="(form.errors as Record<string, string>)[`modifier_items.${index}.price`] || ''" />
                            </div>
                        </div>

                        <div class="grid gap-2">
                            <Label :for="`modifier_item_status_${index}`">{{ t('Status') }}</Label>
                            <Select v-model="form.modifier_items[index].status">
                                <SelectTrigger class="w-full">
                                    <SelectValue :placeholder="t('Select status')" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                                    <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                                </SelectContent>
                            </Select>
                            <InputError :message="(form.errors as Record<string, string>)[`modifier_items.${index}.status`] || ''" />
                        </div>
                    </div>

                    <Button type="button" variant="outline" @click="addMenuModifierItem">
                        {{ t('Add option') }}
                    </Button>
                </div>

                <InputError :message="form.errors.modifier_items" />
            </div>
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>

<style scoped></style>
