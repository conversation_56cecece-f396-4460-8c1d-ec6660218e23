<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/merchants/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Market, Merchant } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    merchants: Merchant[];
    market: Market;
}

const { merchants } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="merchants" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.merchants.create', { currentMarket: market.id })">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
