<script setup lang="ts">
import InputAddressComponent from '@/components/admin/InputAddressComponent.vue';
import FormSelectLazy from '@/components/FormSelectLazy.vue';
import FormText from '@/components/FormText.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';
import CheckboxList from '@/components/ui/checkbox-list/CheckboxList.vue';
import { Input } from '@/components/ui/input';
import { InputImages } from '@/components/ui/input-images';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { t } from '@/lib/utils';
import { CheckboxItem, Choice, Merchant, MerchantFormData } from '@/types';
import { InertiaForm, Link, useForm } from '@inertiajs/vue3';

interface Props {
    model?: Merchant;
    formData?: MerchantFormData;
    categories: CheckboxItem[];
}

const props = withDefaults(defineProps<Props>(), {
    formData: () => ({
        name: '',
        account_id: '',
        address_component: {
            name: '',
            place_id: '',
            formatted_address: '',
            address: '',
            administrative_area_level_3: '',
            administrative_area_level_2: '',
            administrative_area_level_1: '',
            country: '',
            postal_code: '',
            latitude: null,
            longitude: null,
            source: 'google',
        },
        category_ids: [],
        contact_email: '',
        contact_phone_number: '',
        description: '',
        status: 'pending',
        images: [],
    }),
});

const emit = defineEmits<{
    submit: [form: InertiaForm<MerchantFormData>];
}>();

const form = useForm<MerchantFormData>(props.formData);

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="grid gap-2">
            <Label for="name">{{ t('Name') }}</Label>
            <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" />
            <InputError :message="form.errors.name" />
        </div>

        <div class="grid gap-2">
            <Label for="address">{{ t('Address') }}</Label>

            <InputAddressComponent
                id="address"
                v-model="form.address_component"
                :disabled="form.processing"
                :default-value="form.address_component"
            />

            <InputError :message="form.errors.address_component" />
        </div>

        <div class="grid gap-2">
            <Label for="account-id">{{ t('Owner') }}</Label>

            <FormSelectLazy
                v-model="form.account_id"
                :searching-url="route('admin.helpers.selections.accounts.index')"
                :placeholder="t('Select an account')"
                :choices="model ? [model.account as Choice] : undefined"
            >
                <template v-slot:placeholder="{ choice, choices, placeholder }">
                    <span v-if="choices && choices.length > 0">{{ choices.find((item) => item.id === choice)?.name || placeholder }}</span>
                    <span v-else>{{ placeholder }}</span>
                </template>

                <template v-slot:item="{ choice }">
                    <span>{{ choice.name }}</span>
                </template>
            </FormSelectLazy>
            <InputError :message="form.errors.account_id" />
        </div>

        <div class="grid gap-2">
            <Label for="category-ids">{{ t('Categories') }}</Label>
            <template v-if="categories.length > 0">
                <CheckboxList class="mt-1" id="category-ids" name="category_ids" v-model="form.category_ids" :items="categories" />
            </template>
            <div v-else class="bg-secondary rounded-md border px-2 py-4">
                <CardContent>
                    <p class="text-secondary-foreground text-sm">{{ t('No categories available. Please create categories first.') }}</p>
                    <p>
                        <Link :href="route('admin.merchant-categories.create')" class="text-primary text-sm hover:underline" target="_blank">
                            {{ t('Create new merchant category') }}
                        </Link>
                    </p>
                </CardContent>
            </div>
            <InputError :message="form.errors.category_ids" />
        </div>

        <div class="grid gap-2">
            <Label for="contact-phone-number">{{ t('Phone number') }}</Label>
            <Input id="contact-phone-number" v-model="form.contact_phone_number" type="tel" class="mt-1 block w-full" />
            <InputError :message="form.errors.contact_phone_number" />
        </div>

        <div class="grid gap-2">
            <Label for="contact-email">{{ t('Email address') }}</Label>
            <Input id="contact-email" v-model="form.contact_email" type="email" class="mt-1 block w-full" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.contact_email" />
        </div>

        <div class="grid gap-2">
            <Label for="description">{{ t('Description') }}</Label>
            <Textarea id="description" v-model="form.description" class="mt-1 block w-full" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.description" />
        </div>

        <div class="grid gap-2">
            <Label for="description">{{ t('Images') }}</Label>
            <InputImages v-model="form.images" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.images" />
        </div>

        <div class="grid gap-2">
            <Label for="status">{{ t('Status') }}</Label>
            <Select v-model="form.status">
                <SelectTrigger class="w-full">
                    <SelectValue :placeholder="t('Select status')" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                    <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                    <SelectItem value="pending">{{ t('Pending') }}</SelectItem>
                </SelectContent>
            </Select>
            <InputError :message="form.errors.status" />
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
