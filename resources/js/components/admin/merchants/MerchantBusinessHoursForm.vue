<script lang="ts" setup>
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { TimePicker } from '@/components/ui/time-picker';
import { t } from '@/lib/utils';
import { BusinessHoursFormData, DayOfWeek, Merchant } from '@/types';
import { useForm } from '@inertiajs/vue3';
import { CirclePlus, Trash } from 'lucide-vue-next';

interface Props {
    merchant: Merchant;
}

const form = useForm<BusinessHoursFormData>({
    name: 'default',
    started_at: null,
    ended_at: null,
    hours: {
        mon: null,
        tue: null,
        wed: null,
        thu: null,
        fri: null,
        sat: null,
        sun: null,
    },
});

defineProps<Props>();

const renderDayTitle = (day: DayOfWeek): string => {
    const dayTitles: Record<string, string> = {
        mon: 'Monday',
        tue: 'Tuesday',
        wed: 'Wednesday',
        thu: 'Thursday',
        fri: 'Friday',
        sat: 'Saturday',
        sun: 'Sunday',
    };

    return t(dayTitles[day] || day);
};

const determineIfDayIsClosed = (day: DayOfWeek): boolean => {
    return form.hours[day] === null;
};

const determineIfDayIsOpenedAllDay = (day: DayOfWeek): boolean => {
    return form.hours[day] !== null && form.hours[day].periods.length === 0;
};

const handleChangeDayStatus = (day: DayOfWeek, value: boolean) => {
    if (value) {
        form.hours[day] = null;
    } else {
        form.hours[day] = { periods: [] };
    }
};

const handleChangeAllDayStatus = (day: DayOfWeek, value: boolean) => {
    if (value) {
        form.hours[day] = { periods: [] };
    } else {
        form.hours[day] = { periods: [{ start: '09:00', end: '17:00' }] };
    }
};

const submit = () => {};
</script>

<template>
    <DialogHeader>
        <DialogTitle>{{ t('Edit business hours') }}</DialogTitle>
    </DialogHeader>
    <DialogDescription>
        <form @submit="submit" class="space-y-6">
            <pre>{{ form.hours }}</pre>
            <div class="space-y-2">
                <Card v-for="day in Object.keys(form.hours) as Array<DayOfWeek>" :key="`business-hours-${day}`">
                    <CardHeader class="flex items-center justify-between">
                        <CardTitle>{{ renderDayTitle(day) }}</CardTitle>

                        <div class="flex items-center gap-2">
                            <Switch :default-value="determineIfDayIsClosed(day)" @update:model-value="(value) => handleChangeDayStatus(day, value)" />
                            <span class="text-muted-foreground dark:text-muted-foreground-dark text-sm">
                                {{ t('Closed') }}
                            </span>
                        </div>
                    </CardHeader>
                    <CardContent v-if="!determineIfDayIsClosed(day)">
                        <div class="flex items-center gap-2">
                            <Switch
                                :default-value="determineIfDayIsOpenedAllDay(day)"
                                @update:model-value="(value) => handleChangeAllDayStatus(day, value)"
                            />
                            <span class="text-muted-foreground dark:text-muted-foreground-dark text-sm">
                                {{ t('All day') }}
                            </span>
                        </div>

                        <div class="space-y-2" v-if="!determineIfDayIsOpenedAllDay(day) && form.hours[day]?.periods?.length">
                            <div
                                v-for="(period, periodIndex) in form.hours[day]?.periods"
                                :key="`period-${day}-${periodIndex}`"
                                class="border-border dark:border-border-dark rounded-md border p-3"
                            >
                                <div class="mb-2 flex items-center justify-between">
                                    <span class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium">
                                        {{ t('Time Slot') }} {{ Number(periodIndex) + 1 }}
                                    </span>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        class="text-destructive hover:bg-destructive/10 hover:text-destructive dark:text-destructive-dark dark:hover:bg-destructive-dark/10 dark:hover:text-destructive-dark h-7 px-2"
                                        @click="() => false"
                                    >
                                        <Trash class="mr-1 h-3.5 w-3.5" />
                                        {{ t('Delete') }}
                                    </Button>
                                </div>

                                <div class="flex items-center gap-2">
                                    <div class="grid gap-1.5">
                                        <label
                                            :for="`start-${day}-${periodIndex}`"
                                            class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium"
                                        >
                                            {{ t('Start') }}
                                        </label>

                                        <TimePicker :id="`start-${day}-${periodIndex}`" />
                                    </div>

                                    <div class="text-muted-foreground dark:text-muted-foreground-dark mt-6">{{ t('to') }}</div>

                                    <div class="grid gap-1.5">
                                        <label
                                            :for="`end-${day}-${periodIndex}`"
                                            class="text-muted-foreground dark:text-muted-foreground-dark text-xs font-medium"
                                        >
                                            {{ t('End') }}
                                        </label>
                                        <TimePicker :id="`end-${day}-${periodIndex}`" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <Button variant="outline" class="mt-2 w-full justify-center" @click="() => false">
                            <CirclePlus class="mr-1 h-4 w-4" />
                            {{ t('Add') }}
                        </Button>
                    </CardContent>
                </Card>
            </div>

            <div class="flex items-center gap-4">
                <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

                <Transition
                    enter-active-class="transition ease-in-out"
                    enter-from-class="opacity-0"
                    leave-active-class="transition ease-in-out"
                    leave-to-class="opacity-0"
                >
                    <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
                </Transition>
            </div>
        </form>
    </DialogDescription>
    <DialogFooter> </DialogFooter>
</template>
