<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import SellingTimes from '@/components/SellingTimes.vue';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Merchant, SellingTimeFormData, SharedData } from '@/types';
import { useForm, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    formData: SellingTimeFormData;
    onSuccess?: () => void;
}

const props = defineProps<Props>();

const form = useForm<SellingTimeFormData>(props.formData);

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const submit = (e: Event) => {
    e.preventDefault();

    const submitter = (e as SubmitEvent).submitter as HTMLButtonElement;
    if (submitter?.name !== 'main-submit') {
        return;
    }

    form.put(
        route('admin.merchants.selling-times.update', {
            currentMarket: currentMarket?.id,
            merchant: props.merchant.id,
        }),
        {
            preserveScroll: true,
            onSuccess: () => {
                props.onSuccess?.();

                setTimeout(() => {
                    window.location.reload();
                }, 500);
            },
        },
    );
};
</script>

<template>
    <form @submit="submit" class="space-y-6">
        <div class="mb-4">
            <p class="text-muted-foreground text-sm">
                {{ t('Set business hours for each day of the week. Each day can have up to 3 time slots (morning, afternoon, evening).') }}
            </p>
        </div>

        <SellingTimes :items="form.selling_times" @update:items="(items) => (form.selling_times = items)" :is-editable="true" />
        <InputError :message="form.errors['selling_times']" />
        <template v-for="(_, index) in form.selling_times" :key="index">
            <InputError :message="(form.errors as Record<string, string>)[`selling_times.${index}.day`]" />
            <InputError :message="(form.errors as Record<string, string>)[`selling_times.${index}.is_open`]" />
            <InputError :message="(form.errors as Record<string, string>)[`selling_times.${index}.clusters`]" />
            <template v-for="(_, clusterIndex) in form.selling_times[index].clusters" :key="clusterIndex">
                <InputError :message="(form.errors as Record<string, string>)[`selling_times.${index}.clusters.${clusterIndex}.start`]" />
                <InputError :message="(form.errors as Record<string, string>)[`selling_times.${index}.clusters.${clusterIndex}.end`]" />
            </template>
        </template>

        <div class="flex items-center justify-between border-t pt-6">
            <div class="text-muted-foreground text-sm">
                <div v-if="form.recentlySuccessful" class="flex items-center text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {{ t('Changes saved successfully') }}
                </div>
            </div>

            <div class="flex gap-2">
                <Button name="main-submit" type="submit" :disabled="form.processing" class="min-w-[120px]">
                    <template v-if="form.processing">
                        <svg class="mr-2 h-4 w-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        {{ t('Saving...') }}
                    </template>
                    <template v-else>
                        {{ t('Save changes') }}
                    </template>
                </Button>
            </div>
        </div>
    </form>
</template>
