<script lang="ts" setup>
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
} from '@/components/ui/combobox';
import { cn, debounce, http, t } from '@/lib/utils';
import { AddressComponent, AddressComponentFormData, Choice } from '@/types';
import { useVModel } from '@vueuse/core';
import { Check, LoaderCircle, Search } from 'lucide-vue-next';
import { HTMLAttributes, ref } from 'vue';
import { toast } from 'vue-sonner';

type GoogleMapsSuggestion = {
    place_id: string;
    name: string;
    description: string;
};

const props = defineProps<{
    modelValue?: AddressComponentFormData;
    defaultValue?: AddressComponentFormData;
    class?: HTMLAttributes['class'];
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', payload: AddressComponentFormData): void;
}>();

const modelValue = useVModel(props, 'modelValue', emit, {
    passive: true,
    defaultValue: props.defaultValue,
});
const fetching = ref(false);
const searchTerm = ref('');
const choices = ref<Choice<GoogleMapsSuggestion>[] | undefined>();

const fetchChoices = async () => {
    if (fetching.value) {
        return;
    }

    fetching.value = true;

    try {
        const response = await http.get<Choice<GoogleMapsSuggestion>[]>(route('admin.helpers.selections.google-maps.index'), {
            params: searchTerm.value ? { keyword: searchTerm.value } : {},
        });
        choices.value = response.data;
    } catch {
        toast.error(t('Failed to fetch choices. Please try again later.'));
    } finally {
        fetching.value = false;
    }
};

const fetchChoice = async (placeId: string) => {
    if (fetching.value) {
        return;
    }

    fetching.value = true;

    try {
        const response = await http.get<AddressComponent>(route('admin.helpers.selections.google-maps.show', { placeId }));

        const data = response.data;

        modelValue.value = {
            ...modelValue.value,
            name: data.name,
            place_id: data.place_id,
            formatted_address: data.formatted_address,
            address: data.address,
            administrative_area_level_3: data.administrative_area_level_3,
            administrative_area_level_2: data.administrative_area_level_2,
            administrative_area_level_1: data.administrative_area_level_1,
            country: data.country,
            postal_code: data.postal_code,
            latitude: data.latitude,
            longitude: data.longitude,
            source: 'google',
        };
    } catch {
        toast.error(t('Failed to fetch choices. Please try again later.'));
    } finally {
        fetching.value = false;
    }
};

const fetchChoicesHandler = debounce(fetchChoices, 500);

const updateModelValueHandler = debounce((value: Choice<GoogleMapsSuggestion>) => {
    searchTerm.value = value.description;
    fetchChoice(value.place_id);
}, 500);
</script>

<template>
    <Combobox by="label" :ignore-filter="true" @update:model-value="(val) => updateModelValueHandler(val as Choice<GoogleMapsSuggestion>)">
        <ComboboxAnchor class="w-full">
            <div class="relative w-full items-center">
                <ComboboxInput v-model="searchTerm" @keyup="fetchChoicesHandler" :placeholder="t('Search address...')" />

                <span class="absolute inset-y-0 start-0 flex items-center justify-center px-3">
                    <Search class="text-muted-foreground size-4" />
                </span>
            </div>
        </ComboboxAnchor>

        <ComboboxList class="w-full min-w-[200px]">
            <LoaderCircle v-if="fetching" class="text-muted-foreground h-6 w-6 animate-spin" />

            <ComboboxEmpty v-if="!fetching">{{ t('No results found.') }}</ComboboxEmpty>

            <ComboboxGroup v-if="choices?.length">
                <ComboboxItem v-for="choice in choices" :key="choice.place_id as string" :value="choice">
                    {{ choice.description }}

                    <ComboboxItemIndicator>
                        <Check :class="cn('ml-auto h-4 w-4')" />
                    </ComboboxItemIndicator>
                </ComboboxItem>
            </ComboboxGroup>
        </ComboboxList>
    </Combobox>
</template>
