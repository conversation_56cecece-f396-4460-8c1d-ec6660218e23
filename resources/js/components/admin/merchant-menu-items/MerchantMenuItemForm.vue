<script setup lang="ts">
import FormSelectLazy from '@/components/FormSelectLazy.vue';
import FormText from '@/components/FormText.vue';
import GettingStartedResourceAction from '@/components/GettingStartedResourceAction.vue';
import GettingStartedResourceCard from '@/components/GettingStartedResourceCard.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { InputImage } from '@/components/ui/input-image';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { t } from '@/lib/utils';
import { Merchant, MerchantMenuItemFormData, MerchantMenuModifierGroup } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

interface Props {
    merchant: Merchant;
    formData?: MerchantMenuItemFormData;
    modifierGroups: MerchantMenuModifierGroup[];
}

const props = withDefaults(defineProps<Props>(), {
    formData: () => ({
        name: '',
        description: '',
        price: 0,
        image: '',
        merchant_menu_category_id: '',
        status: 'pending',
        modifier_group_ids: [],
    }),
});

const emit = defineEmits<{
    submit: [form: InertiaForm<MerchantMenuItemFormData>];
}>();

const form = useForm<MerchantMenuItemFormData>(props.formData);
const dialogOpen = ref(false);

const isSelectedModifierGroup = (id: string) => {
    return form.modifier_group_ids.includes(id);
};

const addModifierGroup = (id: string) => {
    form.modifier_group_ids = [...form.modifier_group_ids, id];
};

const removeModifierGroup = (id: string) => {
    form.modifier_group_ids = form.modifier_group_ids.filter((groupId) => groupId !== id);
};

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="grid gap-2">
            <Label for="name">{{ t('Name') }}</Label>
            <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" />
            <InputError :message="form.errors.name" />
        </div>

        <div class="grid gap-2">
            <Label for="description">{{ t('Description') }}</Label>
            <Textarea id="description" v-model="form.description" class="mt-1 block w-full" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.description" />
        </div>

        <div class="grid gap-2">
            <Label for="price">{{ t('Price') }}</Label>
            <Input id="price" v-model="form.price" type="number" class="mt-1 block w-full" />
            <InputError :message="form.errors.price" />
        </div>

        <div class="grid gap-2">
            <Label for="category">{{ t('Category') }}</Label>
            <FormSelectLazy
                v-model="form.merchant_menu_category_id"
                :searching-url="route('admin.helpers.selections.merchant-menu-categories.index') + '?merchant_id=' + props.merchant.id"
                :placeholder="t('Select a category')"
            >
                <template v-slot:placeholder="{ choice, choices, placeholder }">
                    <span v-if="choices && choices.length > 0">{{ choices.find((item) => item.id === choice)?.name || placeholder }}</span>
                    <span v-else>{{ placeholder }}</span>
                </template>

                <template v-slot:item="{ choice }">
                    <span>{{ choice.name }}</span>
                </template>
            </FormSelectLazy>
            <InputError :message="form.errors.merchant_menu_category_id" />
        </div>

        <div class="grid gap-2">
            <Label for="status">{{ t('Status') }}</Label>
            <Select v-model="form.status">
                <SelectTrigger class="w-full">
                    <SelectValue :placeholder="t('Select status')" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                    <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                    <SelectItem value="pending">{{ t('Pending') }}</SelectItem>
                </SelectContent>
            </Select>
            <InputError :message="form.errors.status" />
        </div>

        <div class="grid gap-2">
            <Label for="image">{{ t('Image') }}</Label>
            <InputImage id="image" :preview-url="form.image_url" v-model="form.image" :disabled="form.processing" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.image" />
        </div>

        <div class="mt-6">
            <div class="mb-3 flex items-center justify-between">
                <div class="text-sm font-medium">{{ t('Modifier groups') }}</div>

                <Dialog v-model:open="dialogOpen">
                    <DialogTrigger asChild>
                        <Button variant="outline" @click="dialogOpen = true">
                            {{ t('Add') }}
                        </Button>
                    </DialogTrigger>
                    <DialogContent class="max-h-[90%] overflow-y-auto sm:max-w-8/12">
                        <DialogHeader>
                            <DialogTitle>{{ t('Edit modifier groups') }}</DialogTitle>
                        </DialogHeader>
                        <div v-if="modifierGroups.length > 0" class="grid gap-4 py-4">
                            <div v-for="group in modifierGroups" :key="group.id" class="relative rounded-lg border p-3">
                                <div class="mb-1 text-sm">{{ group.name }}</div>
                                <div class="flex items-center gap-2">
                                    <div class="text-xs">
                                        {{ t('Min') }}: <span class="font-medium">{{ group.selection_min }}</span>
                                    </div>
                                    <div class="text-xs">
                                        {{ t('Max') }}: <span class="font-medium">{{ group.selection_max }}</span>
                                    </div>
                                </div>

                                <Button
                                    v-if="isSelectedModifierGroup(group.id)"
                                    size="sm"
                                    @click="removeModifierGroup(group.id)"
                                    class="absolute top-1/2 right-4 -translate-y-1/2 bg-red-500"
                                >
                                    {{ t('Remove') }}
                                </Button>
                                <Button v-else @click="addModifierGroup(group.id)" size="sm" class="absolute top-1/2 right-4 -translate-y-1/2">
                                    {{ t('Select') }}
                                </Button>
                            </div>
                        </div>
                        <template v-else>
                            <GettingStartedResourceCard
                                :title="t('No merchant menu modifiers yet')"
                                :description="t('Get started and create your first merchant menu modifiers.')"
                            >
                                <GettingStartedResourceAction
                                    :href="
                                        route('admin.merchants.menu-modifier-groups.create', {
                                            currentMarket: props.merchant.market_id,
                                            merchant: merchant.id,
                                        })
                                    "
                                >
                                    {{ t('Create new menu modifier') }}
                                </GettingStartedResourceAction>
                            </GettingStartedResourceCard>
                        </template>

                        <DialogFooter>
                            <Button variant="outline" type="button" @click="dialogOpen = false">
                                {{ t('Done') }}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            <div v-for="group in modifierGroups" :key="group.id">
                <div v-if="form.modifier_group_ids.includes(group.id)" class="mb-3 rounded-lg border p-3">
                    <div class="mb-1 text-sm">{{ group.name }}</div>
                    <div class="flex items-center gap-2">
                        <div class="text-xs">
                            {{ t('Min') }}: <span class="font-medium">{{ group.selection_min }}</span>
                        </div>
                        <div class="text-xs">
                            {{ t('Max') }}: <span class="font-medium">{{ group.selection_max }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="form.modifier_group_ids.length === 0" class="text-center text-sm text-slate-600">
                <GettingStartedResourceCard :title="t('No merchant menu modifiers added yet')" />
            </div>
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
