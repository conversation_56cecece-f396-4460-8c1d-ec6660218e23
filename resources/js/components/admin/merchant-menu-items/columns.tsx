import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { t } from '@/lib/utils';
import type { MerchantMenuItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { ColumnDef } from '@tanstack/vue-table';
import { ArrowUpDown, SquarePen } from 'lucide-vue-next';
import type { ButtonHTMLAttributes } from 'vue';

export const columns: ColumnDef<MerchantMenuItem>[] = [
    {
        id: 'select',
        header: ({ table }) => (
            <Checkbox
                modelValue={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                onUpdate:modelValue={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox modelValue={row.getIsSelected()} onUpdate:modelValue={(value) => row.toggleSelected(!!value)} aria-label="Select row" />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Name')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="font-medium">{row.getValue('name')}</div>,
    },
    {
        accessorKey: 'description',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Description')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="font-medium">{row.getValue('description') || '-'}</div>,
    },
    {
        accessorKey: 'price',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Price')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => {
            const price = row.getValue('price');
            return <div class="font-medium">{price}đ</div>;
        },
    },
    {
        accessorKey: 'status',
        header: t('Status'),
        cell: ({ row }) => <div class="capitalize">{row.getValue('status')}</div>,
    },
    {
        accessorKey: 'created_at',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Created At')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => {
            const date = new Date(row.getValue('created_at'));
            return <div class="font-medium">{date.toLocaleDateString()}</div>;
        },
    },
    {
        id: 'actions',
        header: t('Actions'),
        cell: ({ row }) => {
            const item = row.original;

            return (
                <div class="flex items-center gap-2">
                    <Link href={route('admin.merchants.menus.edit', { currentMarket: item.market_id, merchant: item.merchant_id, menu: item.id })}>
                        <Button variant="default" size="sm" class="bg-primary ml-auto hidden h-8 lg:flex">
                            <SquarePen class="h-4 w-4" />
                            <span class="sr-only">{t('Edit')}</span>
                        </Button>
                    </Link>
                </div>
            );
        },
    },
];
