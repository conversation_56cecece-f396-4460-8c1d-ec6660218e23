<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/merchant-menu-items/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Merchant, MerchantMenuItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    merchantMenuItems: MerchantMenuItem[];
    merchant: Merchant;
}

const { merchantMenuItems } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="merchantMenuItems" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.merchants.menus.create', { currentMarket: merchant.market_id, merchant: merchant.id })">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
