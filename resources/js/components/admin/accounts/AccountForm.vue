<script setup lang="ts">
import FormText from '@/components/FormText.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { t } from '@/lib/utils';
import type { AccountFormData } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';

interface Props {
    formData: AccountFormData;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    submit: [form: InertiaForm<AccountFormData>];
}>();

const form = useForm<AccountFormData>(props.formData);

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="grid gap-2">
            <Label for="name">{{ t('Name') }}</Label>
            <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" autocomplete="name" />
            <InputError :message="form.errors.name" />
        </div>

        <div class="grid gap-2">
            <Label for="phone-number">{{ t('Phone number') }}</Label>
            <Input id="phone-number" v-model="form.phone_number" type="tel" class="mt-1 block w-full" autocomplete="tel" />
            <InputError :message="form.errors.phone_number" />
        </div>

        <div class="grid gap-2">
            <Label for="email">{{ t('Email address') }}</Label>
            <Input id="email" v-model="form.email" type="email" class="mt-1 block w-full" autocomplete="email" />
            <FormText>{{ t('Optional.') }}</FormText>

            <InputError :message="form.errors.email" />
        </div>

        <div class="grid gap-2">
            <Label for="password">{{ t('New password') }}</Label>
            <Input id="password" v-model="form.password" type="password" class="mt-1 block w-full" autocomplete="new-password" />
            <FormText>{{ t('Password required when creating new account.') }}</FormText>
            <InputError :message="form.errors.password" />
        </div>

        <div class="grid gap-2">
            <Label for="password-confirmation">{{ t('Confirm password') }}</Label>
            <Input
                id="password-confirmation"
                v-model="form.password_confirmation"
                type="password"
                class="mt-1 block w-full"
                autocomplete="new-password"
            />
            <InputError :message="form.errors.password_confirmation" />
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
