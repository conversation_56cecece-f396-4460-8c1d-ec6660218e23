<script setup lang="ts">
import { columns } from '@/components/admin/accounts/columns';
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Account } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    accounts: Account[];
}

const { accounts } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="accounts" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.accounts.create')">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
