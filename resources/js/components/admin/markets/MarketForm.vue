<script setup lang="ts">
import MarketAdministrativeAreasForm from '@/components/admin/markets/MarketAdministrativeAreasForm.vue';
import FormSelectLazy from '@/components/FormSelectLazy.vue';
import FormText from '@/components/FormText.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { t } from '@/lib/utils';
import { AdministrativeAreaLevel1, Choice, Market, MarketFormData } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';

interface Props {
    model?: Market;
    formData: MarketFormData;
    administrativeAreasLevel1: AdministrativeAreaLevel1[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
    submit: [form: InertiaForm<MarketFormData>];
}>();

const form = useForm<MarketFormData>(props.formData);

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="grid gap-2">
            <Label for="name">{{ t('Name') }}</Label>
            <Input id="name" v-model="form.name" type="text" class="block w-full" autocomplete="name" />
            <InputError :message="form.errors.name" />
        </div>

        <div class="grid gap-2">
            <Label for="description">{{ t('Description') }}</Label>
            <Textarea id="description" v-model="form.description" type="text" class="block w-full" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.description" />
        </div>

        <div class="grid gap-2">
            <Label for="opening_date">{{ t('Opening date') }}</Label>
            <Input id="opening_date" v-model="form.opening_date" type="date" class="block w-full" />
            <InputError :message="form.errors.opening_date" />
        </div>

        <div class="grid gap-2">
            <Label for="account-id">{{ t('Owner') }}</Label>

            <FormSelectLazy
                v-model="form.account_id"
                :searching-url="route('admin.helpers.selections.accounts.index')"
                :placeholder="t('Select an account')"
                :choices="model ? [model.account as Choice] : undefined"
            >
                <template v-slot:placeholder="{ choice, choices, placeholder }">
                    <span v-if="choices && choices.length > 0">{{ choices.find((item) => item.id === choice)?.name || placeholder }}</span>
                    <span v-else>{{ placeholder }}</span>
                </template>

                <template v-slot:item="{ choice }">
                    <span>{{ choice.name }}</span>
                </template>
            </FormSelectLazy>

            <InputError :message="form.errors.account_id" />
        </div>

        <MarketAdministrativeAreasForm v-model="form.administrative_area" :administrative-areas-level1="administrativeAreasLevel1">
            <InputError
                :message="
                    form.errors['administrative_area.administrative_area_level_1.id'] ||
                    form.errors['administrative_area.administrative_areas_level_2']
                "
            />
        </MarketAdministrativeAreasForm>

        <div class="grid gap-2">
            <Label for="status">{{ t('Status') }}</Label>
            <Select v-model="form.status">
                <SelectTrigger class="w-full">
                    <SelectValue :placeholder="t('Select a status')" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="pending">{{ t('Pending') }}</SelectItem>
                    <SelectItem value="activated">{{ t('Activated') }}</SelectItem>
                    <SelectItem value="deactivated">{{ t('Deactivated') }}</SelectItem>
                </SelectContent>
            </Select>
            <InputError :message="form.errors.status" />
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
