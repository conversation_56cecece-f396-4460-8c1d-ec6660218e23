<script setup lang="ts">
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { http, t } from '@/lib/utils';
import type { AdministrativeAreaLevel1, AdministrativeAreaLevel2, MarketAdministrativeAreaFormData } from '@/types';
import { useVModel } from '@vueuse/core';
import { onMounted, ref } from 'vue';

interface Props {
    modelValue: MarketAdministrativeAreaFormData;
    administrativeAreasLevel1: AdministrativeAreaLevel1[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: 'update:modelValue', payload: MarketAdministrativeAreaFormData): void;
}>();

const modelValue = useVModel(props, 'modelValue', emit, {
    passive: true,
});

const loadedAdministrativeAreasLevel2 = ref<{
    [key: string]: AdministrativeAreaLevel2[];
}>({});

const fetching = ref(false);

const toggleAdministrativeLevel2 = (level2: AdministrativeAreaLevel2) => {
    const area = modelValue.value;

    if (area === undefined) {
        console.warn('No administrative area selected.');

        return;
    }

    const index = area.administrative_areas_level_2.findIndex((item) => item.id === level2.id);

    if (index > -1) {
        area.administrative_areas_level_2.splice(index, 1);
    } else {
        area.administrative_areas_level_2.push(level2);
    }
};

const hasAdministrativeLevel2Checked = (level2: AdministrativeAreaLevel2): boolean => {
    const area = modelValue.value;

    if (area === undefined) {
        console.warn('No administrative area selected.');

        return false;
    }

    return area.administrative_areas_level_2.some((item) => item.id === level2.id);
};

const fetchAdministrativeAreasLevel2 = async (level1Id: AdministrativeAreaLevel1) => {
    const id = level1Id.id;

    if (loadedAdministrativeAreasLevel2.value[id]) {
        return loadedAdministrativeAreasLevel2.value[id];
    }

    try {
        fetching.value = true;
        const response = await http.get<AdministrativeAreaLevel2[]>(
            route('admin.helpers.selections.administrative-areas-level-2.index', {
                administrative_area_level_1: id,
            }),
        );
        loadedAdministrativeAreasLevel2.value[id] = response.data;
    } catch (error) {
        console.error('Failed to fetch administrative areas level 2:', error);
    } finally {
        fetching.value = false;
    }
};

onMounted(() => {
    if (modelValue.value.administrative_area_level_1.id !== '') {
        fetchAdministrativeAreasLevel2(modelValue.value.administrative_area_level_1);
    }
});
</script>

<template>
    <div class="grid gap-2">
        <Label>{{ t('Administrative area level 1') }}</Label>

        <Select
            v-model="modelValue.administrative_area_level_1"
            @update:modelValue="(value) => value && fetchAdministrativeAreasLevel2(value)"
            class="w-full"
        >
            <SelectTrigger class="w-full">
                <SelectValue :placeholder="t('Select an administrative area level 1')" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem v-for="item in administrativeAreasLevel1" :key="item.id" :value="item">
                    {{ item.name }}
                </SelectItem>
            </SelectContent>
        </Select>

        <slot />
    </div>

    <div class="grid gap-2" v-if="loadedAdministrativeAreasLevel2[modelValue.administrative_area_level_1.id]">
        <Label>{{ t('Administrative area level 2') }}</Label>

        <div class="grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4">
            <div
                v-for="(level2, level2Index) in loadedAdministrativeAreasLevel2[modelValue.administrative_area_level_1.id]"
                :key="level2Index"
                class="flex items-center space-x-2"
            >
                <Checkbox
                    :id="`administrative-area-2-${level2Index}`"
                    :value="level2.id"
                    :default-value="hasAdministrativeLevel2Checked(level2)"
                    @update:modelValue="toggleAdministrativeLevel2(level2)"
                />
                <Label :for="`administrative-area-2-${level2Index}`">{{ level2.name }}</Label>
            </div>
        </div>
    </div>
</template>
