<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/markets/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Market } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    markets: Market[];
}

const { markets } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="markets" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.markets.create')">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
