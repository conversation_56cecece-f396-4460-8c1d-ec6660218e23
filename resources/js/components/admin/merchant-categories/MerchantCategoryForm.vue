<script setup lang="ts">
import FormText from '@/components/FormText.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { InputImage } from '@/components/ui/input-image';
import { Label } from '@/components/ui/label';
import { t } from '@/lib/utils';
import type { MerchantCategory, MerchantCategoryFormData } from '@/types';
import { InertiaForm, useForm } from '@inertiajs/vue3';

interface Props {
    model?: MerchantCategory;
    formData?: MerchantCategoryFormData;
}

const props = withDefaults(defineProps<Props>(), {
    formData: () => ({
        name: '',
        image: null,
        image_path: null,
    }),
});

const emit = defineEmits<{
    submit: [form: InertiaForm<MerchantCategoryFormData>];
}>();

const form = useForm<MerchantCategoryFormData>(props.formData);

const submitHandler = () => {
    emit('submit', form);
};
</script>

<template>
    <form @submit.prevent="submitHandler" class="space-y-6">
        <div class="grid gap-2">
            <Label for="name">{{ t('Name') }}</Label>
            <Input id="name" v-model="form.name" type="text" class="mt-1 block w-full" autocomplete="name" />
            <InputError :message="form.errors.name" />
        </div>

        <div class="grid gap-2">
            <Label for="image">{{ t('Image') }}</Label>
            <InputImage id="image" :preview-url="model?.image_url" v-model="form.image" @remove="form.image_path = null" />
            <FormText>{{ t('Optional.') }}</FormText>
            <InputError :message="form.errors.image" />
        </div>

        <div class="flex items-center gap-4">
            <Button :disabled="form.processing">{{ t('Save changes') }}</Button>

            <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
            >
                <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">{{ t('Saved.') }}</p>
            </Transition>
        </div>
    </form>
</template>
