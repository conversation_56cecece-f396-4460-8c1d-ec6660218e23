<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/merchant-categories/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { MerchantCategory } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    merchantCategories: MerchantCategory[];
}

const { merchantCategories } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="merchantCategories" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.merchant-categories.create')">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
