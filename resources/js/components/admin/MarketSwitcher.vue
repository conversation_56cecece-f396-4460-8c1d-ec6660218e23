<script lang="ts" setup>
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenuButton } from '@/components/ui/sidebar';
import { t } from '@/lib/utils';
import { SharedData } from '@/types';
import { router, usePage } from '@inertiajs/vue3';
import { Check, ChevronsUpDown, CircleAlert, Plus, Store } from 'lucide-vue-next';

const page = usePage<SharedData>();

const { currentMarket, availableMarkets } = page.props;
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <SidebarMenuButton size="lg">
                <div class="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Store class="size-4" />
                </div>
                <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">
                        {{ currentMarket?.name || t('No Market Selected') }}
                    </span>
                </div>
                <ChevronsUpDown class="ml-auto" />
            </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg" align="start">
            <DropdownMenuLabel class="text-muted-foreground text-xs">
                {{ t('Markets') }}
            </DropdownMenuLabel>

            <template v-if="availableMarkets && availableMarkets.length > 0">
                <DropdownMenuItem
                    v-for="market in availableMarkets"
                    :key="market.id"
                    @click="router.visit(route('admin.dashboard', { currentMarket: market.id }))"
                    class="flex items-center justify-between"
                >
                    <div class="flex items-center gap-2">
                        <span :title="t('This market is pending activation.')">{{ market.name }}</span>

                        <CircleAlert v-if="market.status === 'pending'" class="size-4 text-amber-600 dark:text-amber-400" />
                    </div>

                    <Check class="size-4" v-if="currentMarket && currentMarket.id === market.id" />
                </DropdownMenuItem>
            </template>

            <template v-else>
                <DropdownMenuItem>
                    <div class="text-muted-foreground block w-full py-4 text-center">{{ t('No markets yet') }}</div>
                </DropdownMenuItem>
            </template>

            <DropdownMenuSeparator />

            <DropdownMenuItem class="gap-2 p-2" @click="router.visit(route('admin.markets.create'))">
                <div class="bg-background flex size-6 items-center justify-center rounded-md border">
                    <Plus class="size-4" />
                </div>
                <div class="text-muted-foreground font-medium">{{ t('Create new market') }}</div>
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
