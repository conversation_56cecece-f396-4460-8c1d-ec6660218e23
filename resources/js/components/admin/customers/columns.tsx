import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { t } from '@/lib/utils';
import type { Customer } from '@/types';
import { Link } from '@inertiajs/vue3';
import { ColumnDef } from '@tanstack/vue-table';
import { ArrowUpDown, SquarePen } from 'lucide-vue-next';
import type { ButtonHTMLAttributes } from 'vue';

export const columns: ColumnDef<Customer>[] = [
    {
        id: 'select',
        header: ({ table }) => (
            <Checkbox
                modelValue={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                onUpdate:modelValue={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox modelValue={row.getIsSelected()} onUpdate:modelValue={(value) => row.toggleSelected(!!value)} aria-label="Select row" />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Name')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="font-medium">{row.getValue('name')}</div>,
    },
    {
        accessorKey: 'email',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Email')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="lowercase">{row.getValue('email') || '-'}</div>,
    },
    {
        accessorKey: 'phone_number',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Phone Number')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="font-medium">{row.getValue('phone_number')}</div>,
    },
    {
        accessorKey: 'referral_code',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Referral code')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div class="font-medium">{row.getValue('referral_code')}</div>,
    },
    {
        accessorKey: 'created_at',
        header: ({ column }) => (
            <Button variant="ghost" {...({ onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') } as ButtonHTMLAttributes)}>
                {t('Created At')}
                <ArrowUpDown class="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => {
            const date = new Date(row.getValue('created_at'));
            return <div class="font-medium">{date.toLocaleDateString()}</div>;
        },
    },
    {
        id: 'actions',
        header: t('Actions'),
        cell: ({ row }) => {
            const item = row.original;

            return (
                <div class="flex items-center gap-2">
                    <Link href={route('admin.customers.edit', item.id)}>
                        <Button variant="default" size="sm" class="bg-primary ml-auto hidden h-8 lg:flex">
                            <SquarePen class="h-4 w-4" />
                            <span class="sr-only">{t('Edit')}</span>
                        </Button>
                    </Link>
                </div>
            );
        },
    },
];
