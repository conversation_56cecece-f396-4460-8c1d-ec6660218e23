<script setup lang="ts">
import BaseDataTable from '@/components/admin/BaseDataTable.vue';
import { columns } from '@/components/admin/customers/columns';
import { Button } from '@/components/ui/button';
import { t } from '@/lib/utils';
import type { Customer } from '@/types';
import { Link } from '@inertiajs/vue3';
import { CirclePlus } from 'lucide-vue-next';

interface Props {
    customers: Customer[];
}

const { customers } = defineProps<Props>();
</script>

<template>
    <BaseDataTable :data="customers" :columns="columns">
        <template v-slot:header-actions>
            <Link :href="route('admin.customers.create')">
                <Button>
                    <CirclePlus class="me-1" />
                    {{ t('Create') }}
                </Button>
            </Link>
        </template>
    </BaseDataTable>
</template>
