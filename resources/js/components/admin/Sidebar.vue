<script setup lang="ts">
import MarketSwitcher from '@/components/admin/MarketSwitcher.vue';
import AppLogo from '@/components/AppLogo.vue';
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { t } from '@/lib/utils';
import { NavGroup, type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { LayoutGrid, MapPinHouse, Store, Tag, Users } from 'lucide-vue-next';

const mainNavGroupItems: NavGroup[] = [
    {
        title: t('Platform'),
        items: [
            {
                title: t('Dashboard'),
                route: 'admin.dashboard',
                icon: LayoutGrid,
                requiredMarket: true,
            },
            {
                title: t('Merchants'),
                route: 'admin.merchants.index',
                icon: Store,
                requiredMarket: true,
            },
            {
                title: t('Customers'),
                route: 'admin.customers.index',
                icon: Users,
            },
        ],
    },
    {
        title: t('System'),
        items: [
            {
                title: t('Accounts'),
                route: 'admin.accounts.index',
                icon: Users,
            },
            {
                title: t('Markets'),
                route: 'admin.markets.index',
                icon: MapPinHouse,
            },
            {
                title: t('Merchant categories'),
                route: 'admin.merchant-categories.index',
                icon: Tag,
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    // TODO
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('admin.index')">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                    <MarketSwitcher />
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :groupItems="mainNavGroupItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter v-if="footerNavItems.length > 0" :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
