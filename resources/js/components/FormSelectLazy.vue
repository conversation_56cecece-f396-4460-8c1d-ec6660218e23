<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/components/ui/combobox';
import { debounce, http, t } from '@/lib/utils';
import { Choice } from '@/types';
import { useVModel } from '@vueuse/core';
import { Check, ChevronsUpDown, LoaderCircle, Search } from 'lucide-vue-next';
import { ref, watch } from 'vue';
import { toast } from 'vue-sonner';

const props = withDefaults(
    defineProps<{
        searchingUrl: string;
        defaultValue?: string | number;
        modelValue?: string | number;
        choices?: Choice[];
        placeholder?: string;
    }>(),
    { placeholder: t('Select an option') },
);

const emits = defineEmits<{
    (e: 'update:modelValue', payload: string | number): void;
}>();

const modelValue = useVModel(props, 'modelValue', emits, { passive: true, defaultValue: props.defaultValue });
const fetching = ref(false);
const searchTerm = ref('');
const choices = ref<Choice[] | undefined>(props.choices);

const fetchChoices = async () => {
    if (fetching.value) {
        return;
    }

    fetching.value = true;

    try {
        const response = await http.get(props.searchingUrl, { params: searchTerm.value ? { keyword: searchTerm.value } : {} });
        choices.value = response.data.data;
    } catch {
        toast.error(t('Failed to fetch choices. Please try again later.'));
    } finally {
        fetching.value = false;
    }
};

watch(searchTerm, debounce(fetchChoices, 500));
</script>

<template>
    <Combobox v-model="modelValue" by="label" :ignore-filter="true" @update:open="() => !choices && fetchChoices()" class="w-full">
        <ComboboxAnchor as-child>
            <ComboboxTrigger as-child>
                <Button variant="outline" class="w-full justify-between">
                    <slot name="placeholder" v-bind="{ choice: modelValue, choices, placeholder }">
                        {{ modelValue || placeholder }}
                    </slot>
                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </ComboboxTrigger>
        </ComboboxAnchor>

        <ComboboxList class="w-full">
            <ComboboxInput v-model="searchTerm" class="h-10 border-0 ps-2 focus-visible:ring-0" :placeholder="t('Type to search...')" />

            <Search v-if="!fetching" class="text-muted-foreground absolute inset-y-0 start-0 size-4 px-3" />

            <LoaderCircle v-if="fetching" class="text-muted-foreground h-6 w-6 animate-spin" />

            <ComboboxEmpty v-if="!fetching">{{ t('No results found.') }}</ComboboxEmpty>

            <ComboboxGroup v-if="choices?.length">
                <ComboboxItem v-for="choice in choices" :key="choice.id" :value="choice.id">
                    <slot name="item" :choice="choice">
                        <pre>{{ choice }}</pre>
                    </slot>

                    <ComboboxItemIndicator><Check class="ml-auto h-4 w-4" /></ComboboxItemIndicator>
                </ComboboxItem>
            </ComboboxGroup>
        </ComboboxList>
    </Combobox>
</template>
