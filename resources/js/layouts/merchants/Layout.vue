<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { isRouteActive, t } from '@/lib/utils';
import type { Merchant, NavItem, SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';

interface Props {
    merchant: Merchant;
    layout?: 'default' | 'full';
}

const props = withDefaults(defineProps<Props>(), {
    layout: 'default',
});

const page = usePage<SharedData>();
const { currentMarket } = page.props;

const sidebarNavItems: NavItem[] = [
    {
        title: t('Merchant'),
        route: 'admin.merchants.index',
        href: route('admin.merchants.show', { currentMarket: currentMarket?.id, merchant: props.merchant.id }),
    },
    {
        title: t('Menu categories'),
        route: 'admin.merchants.menu-categories.index',
        href: route('admin.merchants.menu-categories.index', { currentMarket: currentMarket?.id, merchant: props.merchant.id }),
    },
    {
        title: t('Menu modifier groups'),
        route: 'admin.merchants.menu-modifier-groups.index',
        href: route('admin.merchants.menu-modifier-groups.index', { currentMarket: currentMarket?.id, merchant: props.merchant.id }),
    },
    {
        title: t('Menus'),
        route: 'admin.merchants.menus.index',
        href: route('admin.merchants.menus.index', { currentMarket: currentMarket?.id, merchant: props.merchant.id }),
    },
];
</script>

<template>
    <div class="px-4 py-6">
        <Heading :title="t('Merchant information')" />

        <div class="flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-y-0 lg:space-x-12">
            <aside class="w-full max-w-xl lg:w-48">
                <nav class="flex flex-col space-y-1 space-x-0">
                    <Button
                        v-for="item in sidebarNavItems"
                        :key="item.route"
                        variant="ghost"
                        :class="['w-full justify-start', { 'bg-muted': isRouteActive(item.route) }]"
                        as-child
                    >
                        <Link :href="item.href || route(item.route)">
                            {{ item.title }}
                        </Link>
                    </Button>
                </nav>
            </aside>

            <Separator class="my-6 md:hidden" />

            <div v-if="props.layout === 'default'" class="flex-1 md:max-w-2xl">
                <section class="max-w-xl space-y-12">
                    <slot />
                </section>
            </div>
            <div v-else class="flex-1">
                <section class="space-y-12">
                    <slot />
                </section>
            </div>
        </div>
    </div>
</template>
