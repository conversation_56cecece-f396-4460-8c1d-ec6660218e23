<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { isRouteActive, t } from '@/lib/utils';
import type { NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';

const sidebarNavItems: NavItem[] = [
    {
        title: t('Profile'),
        route: 'admin.profile.edit',
    },
    {
        title: t('Password'),
        route: 'admin.password.edit',
    },
    {
        title: t('Appearance'),
        route: 'admin.appearance',
    },
];
</script>

<template>
    <div class="px-4 py-6">
        <Heading :title="t('Settings')" :description="t('Manage your profile and account settings')" />

        <div class="flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-y-0 lg:space-x-12">
            <aside class="w-full max-w-xl lg:w-48">
                <nav class="flex flex-col space-y-1 space-x-0">
                    <Button
                        v-for="item in sidebarNavItems"
                        :key="item.route"
                        variant="ghost"
                        :class="['w-full justify-start', { 'bg-muted': isRouteActive(item.route) }]"
                        as-child
                    >
                        <Link :href="route(item.route)">
                            {{ item.title }}
                        </Link>
                    </Button>
                </nav>
            </aside>

            <Separator class="my-6 md:hidden" />

            <div class="flex-1 md:max-w-2xl">
                <section class="max-w-xl space-y-12">
                    <slot />
                </section>
            </div>
        </div>
    </div>
</template>
