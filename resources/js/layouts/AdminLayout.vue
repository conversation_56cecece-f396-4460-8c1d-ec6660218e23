<script setup lang="ts">
import Sidebar from '@/components/admin/Sidebar.vue';
import { Toaster } from '@/components/ui/sonner';
import AppLayout from '@/layouts/app/AppSidebarLayout.vue';
import type { BreadcrumbItem, SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { watch } from 'vue';
import { toast } from 'vue-sonner';

interface Props {
    breadcrumbs?: BreadcrumbItem[];
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});

const page = usePage<SharedData>();

watch(
    () => page.props.flash,
    (flash) => {
        if (!flash) return;

        if (flash.error) {
            toast.error(flash.error);
        } else if (flash.success) {
            toast.success(flash.success);
        }
    },
    { immediate: true },
);
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <template #sidebar>
            <Sidebar />
        </template>
        <slot />
    </AppLayout>

    <Toaster />
</template>
