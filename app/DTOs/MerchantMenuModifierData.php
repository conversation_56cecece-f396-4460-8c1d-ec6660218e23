<?php

declare(strict_types=1);

namespace App\DTOs;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;

/**
 * @implements Arrayable<string, string|mixed>
 */
final readonly class MerchantMenuModifierData implements Arrayable
{
    public function __construct(
        public ?string $id,
        public string $name,
        public int $price,
        public string $status,
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            id: Arr::get($data, 'id'),
            name: Arr::get($data, 'name'),
            price: (int) Arr::get($data, 'price'),
            status: Arr::get($data, 'status'),
        );
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'price' => $this->price,
            'status' => $this->status,
        ];
    }
}
