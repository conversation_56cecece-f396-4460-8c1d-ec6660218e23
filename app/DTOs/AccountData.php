<?php

declare(strict_types=1);

namespace App\DTOs;

use App\Http\Requests\Admin\Accounts\CreateAccountRequest;
use App\Http\Requests\Admin\Accounts\UpdateAccountRequest;
use App\Models\Account;
use Illuminate\Contracts\Support\Arrayable;

/**
 * @implements Arrayable<string, string|null>
 */
final readonly class AccountData implements Arrayable
{
    public function __construct(
        public string $name,
        public string $phoneNumber,
        public ?string $email,
        public ?string $password,
    ) {}

    public static function fromCreateAccountRequest(CreateAccountRequest $request): self
    {
        return new self(
            name: $request->input('name'),
            phoneNumber: $request->input('phone_number'),
            email: $request->input('email'),
            password: $request->input('password'),
        );
    }

    public static function fromUpdateAccountRequest(UpdateAccountRequest $request): self
    {
        return new self(
            name: $request->input('name'),
            phoneNumber: $request->input('phone_number'),
            email: $request->input('email'),
            password: $request->input('password'),
        );
    }

    public static function fromAccountModel(Account $account): self
    {
        return new self(
            name: $account->name,
            phoneNumber: $account->phone_number,
            email: $account->email,
            password: null,
        );
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'phone_number' => $this->phoneNumber,
            'email' => $this->email,
            'password' => $this->password,
        ];
    }
}
