<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\Merchants;

use App\Actions\Merchant\UpdateMerchantSellingTimeAction;
use App\DTOs\MerchantSellingTimeClusterData;
use App\DTOs\MerchantSellingTimeData;
use App\DTOs\MerchantSellingTimeItemData;
use App\Http\Requests\Admin\Merchants\UpdateSellingTimeRequest;
use App\Models\Market;
use App\Models\Merchant;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

final class UpdateSellingTimeController
{
    public function __invoke(
        Market $currentMarket,
        Merchant $merchant,
        UpdateSellingTimeRequest $request,
        UpdateMerchantSellingTimeAction $updateMerchantSellingTimeAction,
    ): RedirectResponse {
        $sellingTimes = $request->input('selling_times', []);

        $data = new Collection();

        foreach ($sellingTimes as $sellingTime) {
            $sellingTimeItem = MerchantSellingTimeItemData::fromArray($sellingTime);
            if ($clusters = Arr::get($sellingTime, 'clusters', [])) {
                $clustersCollection = new Collection();
                foreach ($clusters as $cluster) {
                    $clustersCollection->push(
                        MerchantSellingTimeClusterData::fromArray($cluster),
                    );
                }

                $sellingTimeItem = $sellingTimeItem->withClusters($clustersCollection);
            }


            $data->push($sellingTimeItem);
        }

        $updateMerchantSellingTimeAction->handle(
            $merchant,
            MerchantSellingTimeData::create($data),
        );

        return back()->with('success', __('Business hours updated successfully.'));
    }
}
