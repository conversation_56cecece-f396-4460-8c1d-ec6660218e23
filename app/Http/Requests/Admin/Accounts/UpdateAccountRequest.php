<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Accounts;

use App\Models\Account;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

final class UpdateAccountRequest extends FormRequest
{
    /**
     * @return array<string, array<int, mixed>>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],

            'phone_number' => [
                'required',
                'string',
                'regex:/^0[0-9]{9}$/',
                Rule::unique(Account::class)->ignore($this->route('account')),
            ],

            'email' => [
                'nullable',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(Account::class)->ignore($this->route('account')),
            ],

            'password' => [
                'nullable',
                Password::defaults(),
                'confirmed',
            ],
        ];
    }
}
