<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Merchants;

use App\Rules\TimeClusterRule;
use Illuminate\Foundation\Http\FormRequest;

final class UpdateSellingTimeRequest extends FormRequest
{
    /**
     * @return array<string, array<int, mixed>>
     */
    public function rules(): array
    {
        $rules = [
            'selling_times' => ['required', 'array'],
            'selling_times.*.day' => ['required', 'string'],
            'selling_times.*.is_open' => ['required', 'boolean'],
            'selling_times.*.is_full' => ['required', 'boolean'],
            'selling_times.*.clusters.*.start' => ['required', 'date_format:H:i'],
            'selling_times.*.clusters.*.end' => ['required', 'date_format:H:i'],
        ];

        $data = $this->input();
        foreach ($data['selling_times'] ?? [] as $index => $sellingTime) {
            $rules["selling_times.{$index}.clusters"] = [
                'nullable',
                'array',
                new TimeClusterRule($sellingTime['day']),
            ];
        }

        return $rules;
    }
}
