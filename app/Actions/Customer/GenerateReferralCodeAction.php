<?php

declare(strict_types=1);

namespace App\Actions\Customer;

use App\Models\Customer;
use Illuminate\Support\Str;

final readonly class GenerateReferralCodeAction
{
    public function handle(): string
    {
        do {
            $referralCode = Str::upper(Str::random(12));
        } while ($this->referralCodeExists($referralCode));

        return $referralCode;
    }

    private function referralCodeExists(string $referralCode): bool
    {
        return Customer::query()
            ->where('referral_code', $referralCode)
            ->exists();
    }

}
