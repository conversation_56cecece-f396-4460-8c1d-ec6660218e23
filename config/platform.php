<?php

declare(strict_types=1);

return [
    'admin' => [
        'domain' => env('PLATFORM_ADMIN_DOMAIN', parse_url((string) env('PLATFORM_ADMIN_URL', env('APP_URL', 'http://localhost')), PHP_URL_HOST)),

        'prefix' => env('PLATFORM_ADMIN_PREFIX', parse_url((string) env('PLATFORM_ADMIN_URL', env('APP_URL', 'http://localhost')), PHP_URL_PATH)),
    ],

    'api' => [
        'domain' => env('PLATFORM_API_DOMAIN', parse_url((string) env('PLATFORM_API_URL', env('APP_URL', 'http://localhost')), PHP_URL_HOST)),

        'prefix' => env('PLATFORM_API_PREFIX', parse_url((string) env('PLATFORM_API_URL', env('APP_URL', 'http://localhost')), PHP_URL_PATH)),
    ],

    'filesystem_disk' => env('PLATFORM_FILESYSTEM_DISK', env('FILESYSTEM_DISK', 'local')),
];
